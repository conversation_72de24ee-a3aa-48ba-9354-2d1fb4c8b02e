<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;
use Exception;

class AITestController extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $data['title'] = 'AI Test - Reports Analysis';
        $data['active'] = 'ai-test';
        $data['userDetails'] = $this->usermodel->find($this->decrypt_cookie_value(get_cookie('user_session')));
        $data['customScript'] = 'ai-test';
        $data['main_content'] = 'pages/ai-test';

        return view('includes/template', $data);
    }

    public function analyzeReports()
    {
        try {
            $authCheck = $this->checkAuthentication();
            if ($authCheck !== true) {
                // For AJAX requests, return JSON instead of redirect
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Authentication required. Please refresh the page and try again.',
                    'redirect' => true
                ]);
            }

            $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

            // Get date range data from request
            $requestData = json_decode($this->request->getBody(), true);
            $dateRangeData = $requestData['dateRange'] ?? null;

            // Log the received date range for debugging
            if ($dateRangeData) {
                log_message('info', 'AI Analysis Date Range: ' . json_encode($dateRangeData));
            }

            // Get reports data with date range
            $reportsData = $this->getReportsDataForAI($userId, $dateRangeData);

            // Call DeepSeek API
            $aiResponse = $this->callDeepSeekAPI($reportsData);

            // Log successful response for debugging
            log_message('info', 'AI Analysis successful - Response length: ' . strlen($aiResponse['analysis']) . ' characters');

            return $this->response->setJSON([
                'success' => true,
                'data' => $aiResponse,
                'reports_summary' => $reportsData['performance'],
                'period' => $reportsData['period']
            ]);

        } catch (Exception $e) {
            log_message('error', 'AI Test Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Analysis failed: ' . $e->getMessage()
            ]);
        }
    }

    // Debug method to test API without authentication (remove in production)
    public function debugAPI()
    {
        try {
            // Simple test with minimal data to verify API works
            $testData = [
                'period' => 'Test Period',
                'performance' => [
                    'total_trades' => 20,
                    'wins' => 12,
                    'losses' => 8,
                    'breakevens' => 0,
                    'win_rate' => 60,
                    'total_pnl' => 15000,
                    'expectancy' => 750,
                    'avg_win' => 2000,
                    'avg_loss' => -1000,
                    'avg_confidence' => 7.5,
                    'avg_rr_ratio' => 2.1
                ],
                'calendar' => [
                    'trading_days' => 15,
                    'avg_trades_per_day' => 1.3,
                    'best_day' => ['date' => '2024-01-15', 'pnl' => 3000],
                    'worst_day' => ['date' => '2024-01-08', 'pnl' => -1500]
                ],
                'mistakes' => ['total_mistakes' => 3],
                'rules' => ['total_rule_usage' => 5],
                'strategies' => [
                    ['name' => 'Breakout', 'trades' => 10, 'win_rate' => 70, 'total_pnl' => 8000, 'profit_factor' => 2.5]
                ],
                'risk_management' => [
                    'planned_r_multiple' => 2.0,
                    'realized_r_multiple' => 1.8,
                    'avg_loss' => -1000,
                    'max_drawdown' => 2500,
                    'expectancy' => 0.8,
                    'target_achievement_rate' => 65,
                    'total_trades_with_targets' => 18
                ],
                'emotional_state' => [
                    'total_emotional_trades' => 20,
                    'dominant_emotion' => 'Confident',
                    'dominant_emotion_performance' => 1200,
                    'emotions_breakdown' => [
                        ['emotion' => 'Confident', 'count' => 12, 'percentage' => 60, 'avg_pnl' => 1200]
                    ]
                ],
                'symbols' => [
                    'total_symbols_traded' => 8,
                    'most_traded_symbol' => 'RELIANCE',
                    'most_traded_performance' => 4000,
                    'symbol_concentration' => 25,
                    'top_symbols' => [
                        ['symbol' => 'RELIANCE', 'trades' => 5, 'total_pnl' => 4000, 'win_rate' => 80]
                    ]
                ],
                'capital_usage' => [
                    'avg_capital_per_trade' => 50000,
                    'max_capital_used' => 100000,
                    'min_capital_used' => 25000,
                    'capital_efficiency' => 30,
                    'total_capital_deployed' => 1000000
                ],
                'trading_patterns' => [
                    'avg_trades_per_day' => 1.3,
                    'max_trades_per_day' => 3,
                    'overtrading_days' => 2,
                    'best_trading_day' => 'Monday',
                    'consecutive_winning_days' => 3,
                    'consecutive_losing_days' => 2,
                    'total_trading_days' => 15
                ]
            ];

            $aiResponse = $this->callDeepSeekAPI($testData);

            return $this->response->setJSON([
                'success' => true,
                'data' => $aiResponse,
                'raw_data' => $testData,
                'debug' => true
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Debug API failed: ' . $e->getMessage(),
                'debug' => true
            ]);
        }
    }

    // Simple test method to verify API connectivity
    public function testAPI()
    {
        try {
            $simplePrompt = "Analyze this trading data:\n\nTrades: 10 (6 wins, 4 losses)\nWin Rate: 60%\nTotal P&L: &#8377;5,000\n\nProvide analysis in this format:\n\n1. PERFORMANCE\nBrief assessment\n\n2. STRENGTHS\n• Key strength\n\n3. WEAKNESSES\n• Key weakness\n\n4. ACTIONS\n• Priority action\n\n5. PSYCHOLOGY\nBehavioral insight";

            $data = [
                'model' => 'deepseek-chat',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a trading analyst. Provide structured analysis in the exact format requested.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $simplePrompt
                    ]
                ],
                'stream' => false,
                'temperature' => 0.1,
                'max_tokens' => 1000
            ];

            $apiKey = 'sk-99a838f6e8a14807b37abca52412d2a7';
            $url = 'https://api.deepseek.com/chat/completions';

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $decodedResponse = json_decode($response, true);
            $analysisContent = $decodedResponse['choices'][0]['message']['content'] ?? 'No response';

            return $this->response->setJSON([
                'success' => true,
                'http_code' => $httpCode,
                'analysis' => $analysisContent,
                'raw_response' => $response
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Test API failed: ' . $e->getMessage()
            ]);
        }
    }

    private function getReportsDataForAI($userId, $dateRangeData = null)
    {
        $db = \Config\Database::connect();

        // Handle date range
        if ($dateRangeData && $dateRangeData['type'] === 'custom') {
            $fromDate = $dateRangeData['startDate'];
            $toDate = $dateRangeData['endDate'];
            $period = $dateRangeData['label'];
        } else {
            // Default or preset ranges
            $days = $dateRangeData ? intval($dateRangeData['type']) : 30;
            $fromDate = date('Y-m-d', strtotime("-{$days} days"));
            $toDate = date('Y-m-d');
            $period = $dateRangeData ? $dateRangeData['label'] : 'Last 30 days';
        }

        // Get comprehensive trading data with error handling
        try {
            $data = [
                'period' => $period,
                'performance' => $this->getPerformanceData($userId, $fromDate, $db, $toDate),
                'calendar' => $this->getCalendarData($userId, $fromDate, $db, $toDate),
                'mistakes' => $this->getMistakesData($userId, $fromDate, $db, $toDate),
                'rules' => $this->getRulesData($userId, $fromDate, $db, $toDate),
                'strategies' => $this->getStrategiesData($userId, $fromDate, $db, $toDate),
                'risk_management' => $this->getRiskManagementData($userId, $fromDate, $db, $toDate),
                'emotional_state' => $this->getEmotionalStateData($userId, $fromDate, $db, $toDate),
                'symbols' => $this->getSymbolAnalysisData($userId, $fromDate, $db, $toDate),
                'capital_usage' => $this->getCapitalUsageData($userId, $fromDate, $db, $toDate),
                'trading_patterns' => $this->getTradingPatternsData($userId, $fromDate, $db, $toDate)
            ];

            // Log data collection success
            log_message('info', 'AI Data Collection - Performance: ' . $data['performance']['total_trades'] . ' trades');
            log_message('info', 'AI Data Collection - Risk Management: R:R ' . $data['risk_management']['realized_r_multiple']);
            log_message('info', 'AI Data Collection - Emotions: ' . $data['emotional_state']['total_emotional_trades'] . ' emotional trades');

        } catch (Exception $e) {
            log_message('error', 'AI Data Collection Error: ' . $e->getMessage());
            throw new Exception('Failed to collect trading data: ' . $e->getMessage());
        }

        return $data;
    }

    private function getPerformanceData($userId, $fromDate, $db, $toDate = null)
    {
        // Get Indian market trades only - matching Reports controller logic
        $builder = $db->table('trades t');
        $builder->select('
            t.pnl_amount,
            t.confidence,
            t.rr_ratio,
            t.datetime,
            t.entry_price,
            t.exit_price,
            t.stop_loss,
            s.strategy as strategy_name,
            e.emotion as emotion_name,
            sum.summary as outcome_name
        ');
        $builder->join('strategies s', 's.id = t.strategy', 'left');
        $builder->join('emotions e', 'e.id = t.emotion', 'left');
        $builder->join('summaries sum', 'sum.id = t.outcome', 'left');
        $builder->where('t.user_id', $userId);
        $builder->where('t.deleted_at IS NULL');
        $builder->where('t.datetime >=', $fromDate);
        if ($toDate) {
            $builder->where('t.datetime <=', $toDate . ' 23:59:59');
        }
        $builder->where('t.market_type', '1'); // Filter for Indian market only
        $builder->orderBy('t.datetime', 'DESC');

        $trades = $builder->get()->getResultArray();

        // Calculate metrics using same logic as Reports controller
        $totalTrades = count($trades);
        $wins = array_filter($trades, fn($t) => floatval($t['pnl_amount']) > 0);
        $losses = array_filter($trades, fn($t) => floatval($t['pnl_amount']) < 0);
        $breakevens = array_filter($trades, fn($t) => floatval($t['pnl_amount']) == 0);

        $winCount = count($wins);
        $lossCount = count($losses);
        $breakevenCount = count($breakevens);

        $winRate = $totalTrades > 0 ? round(($winCount / $totalTrades) * 100, 1) : 0;
        $totalPnL = round(array_sum(array_column($trades, 'pnl_amount')), 2);

        // Calculate averages properly
        $avgWin = $winCount > 0 ? round(array_sum(array_column($wins, 'pnl_amount')) / $winCount, 2) : 0;
        $avgLoss = $lossCount > 0 ? round(array_sum(array_column($losses, 'pnl_amount')) / $lossCount, 2) : 0;

        // Expectancy calculation - matching Reports logic
        $expectancy = $totalTrades > 0 ? round(($avgWin * ($winRate / 100)) - (abs($avgLoss) * ((100 - $winRate) / 100)), 2) : 0;

        // Average confidence and R:R ratio
        $avgConfidence = $totalTrades > 0 ? round(array_sum(array_column($trades, 'confidence')) / $totalTrades, 1) : 0;

        // Calculate R:R ratio properly - filter out null/zero values
        $validRRTrades = array_filter($trades, fn($t) => !empty($t['rr_ratio']) && floatval($t['rr_ratio']) > 0);
        $avgRR = count($validRRTrades) > 0 ? round(array_sum(array_column($validRRTrades, 'rr_ratio')) / count($validRRTrades), 2) : 0;

        return [
            'total_trades' => $totalTrades,
            'win_rate' => $winRate,
            'total_pnl' => $totalPnL,
            'expectancy' => $expectancy,
            'avg_win' => $avgWin,
            'avg_loss' => $avgLoss,
            'avg_confidence' => $avgConfidence,
            'avg_rr_ratio' => $avgRR,
            'wins' => $winCount,
            'losses' => $lossCount,
            'breakevens' => $breakevenCount
        ];
    }

    private function getCalendarData($userId, $fromDate, $db, $toDate = null)
    {
        // Get daily trading patterns for Indian market only - matching calendar controller logic
        $whereClause = "user_id = ? AND deleted_at IS NULL AND datetime >= ? AND market_type = '1'";
        $params = [$userId, $fromDate];

        if ($toDate) {
            $whereClause .= " AND datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $dailyData = $db->query("
            SELECT
                DATE(datetime) as trade_date,
                DAYNAME(datetime) as day_name,
                COUNT(*) as trades_count,
                SUM(pnl_amount) as daily_pnl,
                SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as winning_trades,
                AVG(confidence) as avg_confidence,
                AVG(CASE WHEN rr_ratio > 0 THEN rr_ratio ELSE NULL END) as avg_rr
            FROM trades
            WHERE {$whereClause}
            GROUP BY DATE(datetime), DAYNAME(datetime)
            ORDER BY trade_date DESC
        ", $params)->getResultArray();

        // Calculate trading frequency and best/worst days
        $tradingDays = count($dailyData);
        $bestDay = null;
        $worstDay = null;
        $maxPnL = PHP_FLOAT_MIN;
        $minPnL = PHP_FLOAT_MAX;
        $totalTrades = 0;

        foreach ($dailyData as $day) {
            $dailyPnL = floatval($day['daily_pnl']);
            $totalTrades += intval($day['trades_count']);

            if ($dailyPnL > $maxPnL) {
                $maxPnL = $dailyPnL;
                $bestDay = $day;
            }
            if ($dailyPnL < $minPnL) {
                $minPnL = $dailyPnL;
                $worstDay = $day;
            }
        }

        return [
            'trading_days' => $tradingDays,
            'total_trades' => $totalTrades,
            'avg_trades_per_day' => $tradingDays > 0 ? round($totalTrades / $tradingDays, 1) : 0,
            'best_day' => $bestDay ? [
                'date' => $bestDay['trade_date'],
                'pnl' => round(floatval($bestDay['daily_pnl']), 2),
                'trades' => intval($bestDay['trades_count'])
            ] : null,
            'worst_day' => $worstDay ? [
                'date' => $worstDay['trade_date'],
                'pnl' => round(floatval($worstDay['daily_pnl']), 2),
                'trades' => intval($worstDay['trades_count'])
            ] : null
        ];
    }

    private function callDeepSeekAPI($reportsData)
    {
        $apiKey = 'sk-99a838f6e8a14807b37abca52412d2a7';
        $url = 'https://api.deepseek.com/chat/completions';

        // Build analysis prompt
        $prompt = $this->buildAnalysisPrompt($reportsData);

        // Prepare system message with proper escaping
        $systemMessage = 'You are a friendly Indian stock market trading coach. Explain trading analysis in simple, easy-to-understand English as if talking to a beginner. Use everyday language and avoid complex jargon. All monetary values are in Indian Rupees (₹). ' .
                        'CRITICAL LANGUAGE & FORMATTING INSTRUCTIONS: ' .
                        '1. Use EXACTLY the numbered format: "1. PERFORMANCE", "2. STRENGTHS", "3. WEAKNESSES", "4. ACTIONS", "5. PSYCHOLOGY" ' .
                        '2. WRITE IN SIMPLE ENGLISH: Use everyday words that anyone can understand, avoid trading jargon ' .
                        '3. EXPLAIN LIKE TEACHING A FRIEND: Make complex concepts easy to understand ' .
                        '4. MANDATORY: Start each key point with a bullet (•) on a new line ' .
                        '5. NEVER use question marks (?) as bullet points or at the start of sentences ' .
                        '6. NEVER write long paragraphs - break every insight into separate bullet points ' .
                        '7. Format example: "• First insight here. • Second insight here. • Third insight here." ' .
                        '8. Each bullet point should be one complete thought or insight ' .
                        '9. DO NOT use asterisks (**), hash symbols (###), or other markdown formatting ' .
                        '10. Use simple, conversational language with proper punctuation ' .
                        '11. Be specific with data points and actionable recommendations ' .
                        '12. Complete ALL sections fully - do not truncate or abbreviate ' .
                        '13. Use simple, clean formatting - avoid bold, italic, or emphasis markers ' .
                        '14. IMPORTANT: When explaining points, use colons followed by a space (: ) instead of dashes (—) for better readability. Example: "FOMO entries: two impulsive trades cost ₹2,648" instead of "FOMO entries—two impulsive trades cost ₹2,648" ' .
                        'LANGUAGE EXAMPLES: ' .
                        'Instead of "Risk-reward ratio optimization" say "Making sure profits are bigger than losses" ' .
                        'Instead of "Capital deployment efficiency" say "How well you use your money" ' .
                        'Instead of "Breakout strategy execution" say "Buying when price breaks above resistance" ' .
                        'Instead of "Psychological resilience" say "Staying calm and confident while trading" ' .
                        'REMEMBER: Write as if explaining to someone new to trading. Every key insight must be a separate bullet point (•) for maximum readability.';

        $data = [
            'model' => 'deepseek-chat',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemMessage
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'stream' => false,
            'temperature' => 0.1,
            'max_tokens' => 4000
        ];
        
        // Clean and sanitize data before JSON encoding
        $cleanData = $this->sanitizeDataForJson($data);

        // Validate and encode JSON data with proper UTF-8 handling
        $jsonData = json_encode($cleanData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message('error', 'JSON encoding error: ' . json_last_error_msg());
            // Try with different encoding options
            $jsonData = json_encode($cleanData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Failed to encode request data as JSON: ' . json_last_error_msg());
            }
        }

        // Log the request for debugging (truncated)
        log_message('debug', 'DeepSeek API Request: ' . substr($jsonData, 0, 500) . (strlen($jsonData) > 500 ? '...[truncated]' : ''));

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'Accept: application/json'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'TradeDiary/1.0',
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            log_message('error', 'cURL error: ' . $error);
            throw new Exception('Network error: ' . $error);
        }

        if ($response === false) {
            log_message('error', 'Empty response from DeepSeek API');
            throw new Exception('Empty response from API');
        }

        if (empty($response)) {
            log_message('error', 'Empty response body from DeepSeek API');
            throw new Exception('Empty response body from API');
        }

        if ($httpCode !== 200) {
            log_message('error', 'DeepSeek API HTTP error: ' . $httpCode . ' - Response: ' . substr($response, 0, 500));
            throw new Exception('API error: HTTP ' . $httpCode . ' - ' . $response);
        }
        
        $decodedResponse = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message('error', 'DeepSeek API JSON decode error: ' . json_last_error_msg() . ' | Response: ' . substr($response, 0, 500));
            throw new Exception('Invalid JSON response from API');
        }

        // Log the full response for debugging
        $analysisContent = $decodedResponse['choices'][0]['message']['content'] ?? 'No analysis received';
        log_message('info', 'DeepSeek API Response Length: ' . strlen($analysisContent) . ' characters');
        log_message('debug', 'DeepSeek API Full Response: ' . substr($analysisContent, 0, 1000) . (strlen($analysisContent) > 1000 ? '...[truncated]' : ''));

        // Check if response was truncated
        $finishReason = $decodedResponse['choices'][0]['finish_reason'] ?? 'unknown';
        if ($finishReason === 'length') {
            log_message('warning', 'DeepSeek API response was truncated due to token limit');
        }

        // Clean the analysis content to remove excessive formatting
        $cleanedAnalysis = $this->cleanAIResponse($analysisContent);

        return [
            'analysis' => $cleanedAnalysis,
            'usage' => $decodedResponse['usage'] ?? null,
            'model' => $decodedResponse['model'] ?? 'deepseek-chat',
            'finish_reason' => $finishReason
        ];
    }

    private function sanitizeDataForJson($data)
    {
        if (is_array($data)) {
            $cleaned = [];
            foreach ($data as $key => $value) {
                $cleanKey = $this->cleanUtf8String($key);
                $cleaned[$cleanKey] = $this->sanitizeDataForJson($value);
            }
            return $cleaned;
        } elseif (is_string($data)) {
            return $this->cleanUtf8String($data);
        } else {
            return $data;
        }
    }

    private function cleanUtf8String($string)
    {
        if (!is_string($string)) {
            return $string;
        }

        // Remove or replace invalid UTF-8 characters
        $cleaned = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // Remove null bytes and other problematic characters
        $cleaned = str_replace(["\0", "\x00"], '', $cleaned);

        // Remove or replace non-printable characters except common whitespace
        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);

        // Ensure proper UTF-8 encoding
        if (!mb_check_encoding($cleaned, 'UTF-8')) {
            $cleaned = utf8_encode($cleaned);
        }

        return $cleaned;
    }

    private function cleanAIResponse($response)
    {
        if (!is_string($response)) {
            return $response;
        }

        // Remove excessive asterisks and hash symbols
        $cleaned = $response;

        // Remove multiple consecutive asterisks (**, ***, etc.)
        $cleaned = preg_replace('/\*{2,}/', '', $cleaned);

        // Remove multiple consecutive hash symbols (###, ####, etc.)
        $cleaned = preg_replace('/#{2,}/', '', $cleaned);

        // Remove single asterisks used for emphasis when they appear in pairs
        $cleaned = preg_replace('/\*([^*]+)\*/', '$1', $cleaned);

        // Remove hash symbols at the beginning of lines (markdown headers)
        $cleaned = preg_replace('/^#+\s*/m', '', $cleaned);

        // Clean up any remaining single asterisks that might be used for emphasis
        $cleaned = str_replace('*', '', $cleaned);

        // Clean up excessive whitespace that might result from removals
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);

        // Clean up multiple consecutive line breaks
        $cleaned = preg_replace('/\n{3,}/', "\n\n", $cleaned);

        // Trim whitespace from each line
        $lines = explode("\n", $cleaned);
        $lines = array_map('trim', $lines);
        $cleaned = implode("\n", $lines);

        return trim($cleaned);
    }

    private function getMistakesData($userId, $fromDate, $db, $toDate = null)
    {
        // Get mistake frequency and impact - matching Mistake controller logic
        $whereClause = "t.user_id = ? AND t.deleted_at IS NULL AND t.datetime >= ? AND t.market_type = '1' AND m.is_active = 1 AND (m.user_id IS NULL OR m.user_id = ?)";
        $params = [$userId, $fromDate, $userId];

        if ($toDate) {
            $whereClause .= " AND t.datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $mistakes = $db->query("
            SELECT
                m.name,
                m.category,
                m.severity,
                m.impact,
                COUNT(tm.id) as frequency,
                AVG(t.pnl_amount) as avg_impact,
                SUM(t.pnl_amount) as total_impact
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            JOIN mistakes m ON tm.mistake_id = m.id
            WHERE {$whereClause}
            GROUP BY m.id, m.name, m.category, m.severity, m.impact
            ORDER BY frequency DESC
            LIMIT 5
        ", $params)->getResultArray();

        $totalMistakes = array_sum(array_column($mistakes, 'frequency'));
        $mostCommon = $mistakes[0] ?? null;

        // Calculate improvement rate (this week vs last week)
        $thisWeekStart = date('Y-m-d', strtotime('-7 days'));
        $lastWeekStart = date('Y-m-d', strtotime('-14 days'));

        $thisWeekMistakes = $db->query("
            SELECT COUNT(tm.id) as count
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL
            AND t.datetime >= ? AND t.market_type = '1'
        ", [$userId, $thisWeekStart])->getRow()->count ?? 0;

        $lastWeekMistakes = $db->query("
            SELECT COUNT(tm.id) as count
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL
            AND t.datetime >= ? AND t.datetime < ? AND t.market_type = '1'
        ", [$userId, $lastWeekStart, $thisWeekStart])->getRow()->count ?? 0;

        $improvementRate = 0;
        if ($lastWeekMistakes > 0) {
            $improvementRate = round((($lastWeekMistakes - $thisWeekMistakes) / $lastWeekMistakes) * 100, 1);
        }

        return [
            'total_mistakes' => $totalMistakes,
            'most_common' => $mostCommon ? $mostCommon['name'] : 'None',
            'improvement_rate' => $improvementRate,
            'this_week_mistakes' => $thisWeekMistakes,
            'last_week_mistakes' => $lastWeekMistakes,
            'top_mistakes' => array_map(function($mistake) {
                return [
                    'name' => $mistake['name'],
                    'frequency' => intval($mistake['frequency']),
                    'severity' => $mistake['severity'],
                    'category' => $mistake['category'],
                    'avg_impact' => round(floatval($mistake['avg_impact']), 2),
                    'total_impact' => round(floatval($mistake['total_impact']), 2)
                ];
            }, $mistakes)
        ];
    }

    private function getRulesData($userId, $fromDate, $db, $toDate = null)
    {
        // Check if rules table has new structure (name, description, category fields)
        $fields = $db->getFieldNames('rules');
        $hasNewStructure = in_array('name', $fields) && in_array('description', $fields) && in_array('category', $fields);

        if ($hasNewStructure) {
            // New structure query
            $whereClause = "r.deleted_at IS NULL AND t.datetime >= ? AND t.market_type = '1'";
            $params = [$userId, $fromDate];

            if ($toDate) {
                $whereClause .= " AND t.datetime <= ?";
                $params[] = $toDate . ' 23:59:59';
            }

            $rules = $db->query("
                SELECT
                    r.name,
                    r.category,
                    COUNT(tr.id) as total_usage,
                    SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                    SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                    AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_followed,
                    AVG(CASE WHEN tr.followed = 0 THEN t.pnl_amount ELSE NULL END) as avg_pnl_violated
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE {$whereClause}
                GROUP BY r.id, r.name, r.category
                HAVING total_usage > 0
                ORDER BY followed_count DESC
                LIMIT 5
            ", $params)->getResultArray();
        } else {
            // Old structure query
            $whereClause = "t.datetime >= ? AND t.market_type = '1'";
            $params = [$userId, $fromDate];

            if ($toDate) {
                $whereClause .= " AND t.datetime <= ?";
                $params[] = $toDate . ' 23:59:59';
            }

            $rules = $db->query("
                SELECT
                    r.rule as name,
                    'general' as category,
                    COUNT(tr.id) as total_usage,
                    SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                    SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                    AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_followed,
                    AVG(CASE WHEN tr.followed = 0 THEN t.pnl_amount ELSE NULL END) as avg_pnl_violated
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE {$whereClause}
                GROUP BY r.id, r.rule
                HAVING total_usage > 0
                ORDER BY followed_count DESC
                LIMIT 5
            ", $params)->getResultArray();
        }

        $totalRuleUsage = array_sum(array_column($rules, 'total_usage'));
        $totalFollowed = array_sum(array_column($rules, 'followed_count'));
        $overallAdherence = $totalRuleUsage > 0 ? round(($totalFollowed / $totalRuleUsage) * 100, 1) : 0;

        return [
            'overall_adherence' => $overallAdherence,
            'total_rule_usage' => $totalRuleUsage,
            'top_rules' => array_map(function($rule) {
                $totalUsage = intval($rule['total_usage']);
                $followedCount = intval($rule['followed_count']);
                $adherence = $totalUsage > 0 ? round(($followedCount / $totalUsage) * 100, 1) : 0;

                return [
                    'name' => $rule['name'],
                    'category' => $rule['category'] ?? 'general',
                    'adherence_rate' => $adherence,
                    'usage' => $totalUsage,
                    'followed_count' => $followedCount,
                    'violated_count' => intval($rule['violated_count']),
                    'impact_when_followed' => round(floatval($rule['avg_pnl_followed'] ?? 0), 2),
                    'impact_when_violated' => round(floatval($rule['avg_pnl_violated'] ?? 0), 2)
                ];
            }, $rules)
        ];
    }

    private function getStrategiesData($userId, $fromDate, $db, $toDate = null)
    {
        // Get strategy performance data - matching Strategy controller logic
        $whereClause = "t.user_id = ? AND t.deleted_at IS NULL AND t.datetime >= ? AND t.market_type = '1' AND s.deleted_at IS NULL";
        $params = [$userId, $fromDate];

        if ($toDate) {
            $whereClause .= " AND t.datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $strategies = $db->query("
            SELECT
                s.strategy as name,
                s.id as strategy_id,
                COUNT(t.id) as trades_count,
                SUM(CASE WHEN t.pnl_amount > 0 THEN 1 ELSE 0 END) as winning_trades,
                SUM(CASE WHEN t.pnl_amount > 0 THEN t.pnl_amount ELSE 0 END) as total_profit,
                ABS(SUM(CASE WHEN t.pnl_amount < 0 THEN t.pnl_amount ELSE 0 END)) as total_loss,
                SUM(t.pnl_amount) as net_pnl,
                AVG(t.confidence) as avg_confidence,
                AVG(CASE WHEN t.rr_ratio > 0 THEN t.rr_ratio ELSE NULL END) as avg_rr_ratio
            FROM trades t
            JOIN strategies s ON t.strategy = s.id
            WHERE {$whereClause}
            GROUP BY s.id, s.strategy
            ORDER BY net_pnl DESC
            LIMIT 5
        ", $params)->getResultArray();

        return array_map(function($strategy) {
            $tradesCount = intval($strategy['trades_count']);
            $winningTrades = intval($strategy['winning_trades']);
            $winRate = $tradesCount > 0 ? round(($winningTrades / $tradesCount) * 100, 1) : 0;

            // Calculate profit factor like Strategy controller
            $totalProfit = floatval($strategy['total_profit']);
            $totalLoss = floatval($strategy['total_loss']);
            $profitFactor = ($totalLoss > 0) ? round($totalProfit / $totalLoss, 2) : ($totalProfit > 0 ? 'Perfect' : 0);

            return [
                'name' => $strategy['name'],
                'trades' => $tradesCount,
                'win_rate' => $winRate,
                'total_pnl' => round(floatval($strategy['net_pnl']), 2),
                'profit_factor' => $profitFactor,
                'avg_confidence' => round(floatval($strategy['avg_confidence'] ?? 0), 1),
                'avg_rr_ratio' => round(floatval($strategy['avg_rr_ratio'] ?? 0), 2)
            ];
        }, $strategies);
    }

    private function buildAnalysisPrompt($reportsData)
    {
        // Clean all data before building prompt
        $cleanData = $this->sanitizeDataForJson($reportsData);

        $perf = $cleanData['performance'];
        $calendar = $cleanData['calendar'];
        $mistakes = $cleanData['mistakes'];
        $rules = $cleanData['rules'];
        $strategies = $cleanData['strategies'];
        $risk = $cleanData['risk_management'];
        $emotions = $cleanData['emotional_state'];
        $symbols = $cleanData['symbols'];
        $capital = $cleanData['capital_usage'];
        $patterns = $cleanData['trading_patterns'];

        $prompt = "Analyze this comprehensive 30-day trading performance data for detailed insights:\n\n";

        // Performance Overview
        $prompt .= "PERFORMANCE METRICS:\n";
        $prompt .= "• Trades: {$perf['total_trades']} (W:{$perf['wins']} L:{$perf['losses']} BE:{$perf['breakevens']})\n";
        $prompt .= "• Win Rate: {$perf['win_rate']}% | P&L: &#8377;" . number_format($perf['total_pnl'], 2) . "\n";
        $prompt .= "• Expectancy: &#8377;" . number_format($perf['expectancy'], 2) . " per trade\n";
        $prompt .= "• Avg Win: &#8377;" . number_format($perf['avg_win'], 2) . " | Avg Loss: &#8377;" . number_format($perf['avg_loss'], 2) . "\n";
        $prompt .= "• Confidence: {$perf['avg_confidence']}/10 | R:R: {$perf['avg_rr_ratio']}\n\n";

        // Trading Patterns
        $prompt .= "TRADING PATTERNS:\n";
        $prompt .= "• Active Days: {$calendar['trading_days']} | Avg Trades/Day: {$calendar['avg_trades_per_day']}\n";
        if ($calendar['best_day']) {
            $prompt .= "• Best Day: {$calendar['best_day']['date']} (&#8377;" . number_format($calendar['best_day']['pnl'], 2) . ")\n";
        }
        if ($calendar['worst_day']) {
            $prompt .= "• Worst Day: {$calendar['worst_day']['date']} (&#8377;" . number_format($calendar['worst_day']['pnl'], 2) . ")\n";
        }
        $prompt .= "\n";

        // Strategy Performance
        if (!empty($strategies)) {
            $prompt .= "STRATEGY PERFORMANCE:\n";
            foreach (array_slice($strategies, 0, 3) as $strategy) {
                $profitFactor = is_numeric($strategy['profit_factor']) ? $strategy['profit_factor'] : $strategy['profit_factor'];
                $prompt .= "• {$strategy['name']}: {$strategy['trades']} trades, {$strategy['win_rate']}% win rate, &#8377;" . number_format($strategy['total_pnl'], 2) . " (PF: {$profitFactor})\n";
            }
            $prompt .= "\n";
        }

        // Mistakes Analysis
        if ($mistakes['total_mistakes'] > 0) {
            $prompt .= "MISTAKES ANALYSIS:\n";
            $prompt .= "• Total Mistakes: {$mistakes['total_mistakes']} | Most Common: {$mistakes['most_common']}\n";
            $prompt .= "• Improvement Rate: {$mistakes['improvement_rate']}% (This week: {$mistakes['this_week_mistakes']}, Last week: {$mistakes['last_week_mistakes']})\n";
            foreach (array_slice($mistakes['top_mistakes'], 0, 3) as $mistake) {
                $prompt .= "• {$mistake['name']} ({$mistake['severity']}): {$mistake['frequency']} times, total impact &#8377;" . number_format($mistake['total_impact'], 2) . "\n";
            }
            $prompt .= "\n";
        }

        // Rules Adherence
        if ($rules['total_rule_usage'] > 0) {
            $prompt .= "RULES ADHERENCE:\n";
            $prompt .= "• Overall Adherence: {$rules['overall_adherence']}% | Total Rule Usage: {$rules['total_rule_usage']}\n";
            foreach (array_slice($rules['top_rules'], 0, 3) as $rule) {
                $impactDiff = $rule['impact_when_followed'] - $rule['impact_when_violated'];
                $prompt .= "• {$rule['name']}: {$rule['adherence_rate']}% adherence ({$rule['followed_count']}/{$rule['usage']}), impact diff &#8377;" . number_format($impactDiff, 2) . "\n";
            }
            $prompt .= "\n";
        }

        // Risk Management Analysis
        $prompt .= "RISK MANAGEMENT:\n";
        $prompt .= "• Planned R:R: 1:{$risk['planned_r_multiple']} | Realized R:R: 1:{$risk['realized_r_multiple']}\n";
        $prompt .= "• Max Drawdown: &#8377;" . number_format($risk['max_drawdown'], 2) . " | Avg Loss: &#8377;" . number_format($risk['avg_loss'], 2) . "\n";
        $prompt .= "• Target Achievement: {$risk['target_achievement_rate']}% ({$risk['total_trades_with_targets']} trades with targets)\n";
        $prompt .= "• Expectancy: {$risk['expectancy']}\n\n";

        // Emotional State Analysis
        if ($emotions['total_emotional_trades'] > 0) {
            $prompt .= "EMOTIONAL STATE:\n";
            $prompt .= "• Dominant Emotion: {$emotions['dominant_emotion']} (Performance: &#8377;" . number_format($emotions['dominant_emotion_performance'], 2) . ")\n";
            foreach (array_slice($emotions['emotions_breakdown'], 0, 3) as $emotion) {
                $prompt .= "• {$emotion['emotion']}: {$emotion['count']} trades ({$emotion['percentage']}%), avg PnL &#8377;" . number_format($emotion['avg_pnl'], 2) . ", confidence {$emotion['avg_confidence']}/10\n";
            }
            $prompt .= "\n";
        }

        // Symbol Analysis
        if ($symbols['total_symbols_traded'] > 0) {
            $prompt .= "SYMBOL ANALYSIS:\n";
            $prompt .= "• Total Symbols: {$symbols['total_symbols_traded']} | Most Traded: {$symbols['most_traded_symbol']} (Performance: &#8377;" . number_format($symbols['most_traded_performance'], 2) . ")\n";
            $prompt .= "• Symbol Concentration: {$symbols['symbol_concentration']}% in top symbol\n";
            foreach (array_slice($symbols['top_symbols'], 0, 3) as $symbol) {
                $prompt .= "• {$symbol['symbol']}: {$symbol['trades']} trades, {$symbol['win_rate']}% win rate, &#8377;" . number_format($symbol['total_pnl'], 2) . " total PnL\n";
            }
            $prompt .= "\n";
        }

        // Capital Usage
        if ($capital['avg_capital_per_trade'] > 0) {
            $prompt .= "CAPITAL MANAGEMENT:\n";
            $prompt .= "• Avg Capital/Trade: &#8377;" . number_format($capital['avg_capital_per_trade'], 2) . " | Range: &#8377;" . number_format($capital['min_capital_used'], 2) . " - &#8377;" . number_format($capital['max_capital_used'], 2) . "\n";
            $prompt .= "• Capital Efficiency: {$capital['capital_efficiency']}% | Total Deployed: &#8377;" . number_format($capital['total_capital_deployed'], 2) . "\n\n";
        }

        // Trading Patterns
        $prompt .= "TRADING PATTERNS:\n";
        $prompt .= "• Frequency: {$patterns['avg_trades_per_day']} avg/day, {$patterns['max_trades_per_day']} max/day | Overtrading Days: {$patterns['overtrading_days']}\n";
        $prompt .= "• Best Day: {$patterns['best_trading_day']} | Trading Days: {$patterns['total_trading_days']}\n";
        $prompt .= "• Streaks: {$patterns['consecutive_winning_days']} consecutive winning days, {$patterns['consecutive_losing_days']} consecutive losing days\n\n";

        $prompt .= "ANALYSIS REQUIRED - Use EXACT format below:\n\n";

        $prompt .= "1. PERFORMANCE\n";
        $prompt .= "Provide comprehensive assessment considering all metrics including risk management, capital efficiency, and trading patterns. Include specific numbers and percentages from the data above. (4-5 detailed sentences)\n\n";

        $prompt .= "2. STRENGTHS\n";
        $prompt .= "Identify what's working well across strategies, risk management, emotional control, symbol selection, and trading patterns. Use bullet points (•) for each strength with specific data:\n";
        $prompt .= "• [Strength 1 with specific data]\n";
        $prompt .= "• [Strength 2 with specific data]\n";
        $prompt .= "• [Strength 3 with specific data]\n\n";

        $prompt .= "3. WEAKNESSES\n";
        $prompt .= "Identify key issues from mistakes, rule violations, poor risk management, emotional trading, overtrading, and capital inefficiency. Use bullet points (•) for each weakness with specific data:\n";
        $prompt .= "• [Weakness 1 with specific data]\n";
        $prompt .= "• [Weakness 2 with specific data]\n";
        $prompt .= "• [Weakness 3 with specific data]\n\n";

        $prompt .= "4. ACTIONS\n";
        $prompt .= "Provide specific, prioritized next steps for improvement covering strategy, risk management, psychology, and execution. Use bullet points (•) for each action:\n";
        $prompt .= "• [Priority Action 1]\n";
        $prompt .= "• [Priority Action 2]\n";
        $prompt .= "• [Priority Action 3]\n";
        $prompt .= "• [Priority Action 4]\n\n";

        $prompt .= "5. PSYCHOLOGY\n";
        $prompt .= "Provide behavioral insights from confidence levels, emotional state patterns, mistake frequency, rule adherence, and trading discipline. Include specific observations from the data. (3-4 detailed sentences)\n\n";

        $prompt .= "IMPORTANT: Complete ALL sections fully. Use the exact numbered format shown above. Include specific data points and actionable recommendations.";

        // Final cleaning of the prompt
        return $this->cleanUtf8String($prompt);
    }

    private function getRiskManagementData($userId, $fromDate, $db, $toDate = null)
    {
        try {
            // Get risk management metrics similar to Reports controller
            $whereClause = "user_id = ? AND deleted_at IS NULL AND datetime >= ? AND market_type = '1' AND entry_price IS NOT NULL AND exit_price IS NOT NULL";
            $params = [$userId, $fromDate];

            if ($toDate) {
                $whereClause .= " AND datetime <= ?";
                $params[] = $toDate . ' 23:59:59';
            }

            $trades = $db->query("
                SELECT
                    entry_price,
                    exit_price,
                    stop_loss,
                    target,
                    pnl_amount,
                    entry_amount
                FROM trades
                WHERE {$whereClause}
            ", $params)->getResultArray();
        } catch (Exception $e) {
            log_message('error', 'Risk Management Data Error: ' . $e->getMessage());
            return [
                'planned_r_multiple' => 0,
                'realized_r_multiple' => 0,
                'avg_loss' => 0,
                'max_drawdown' => 0,
                'expectancy' => 0,
                'target_achievement_rate' => 0,
                'total_trades_with_targets' => 0
            ];
        }

        if (empty($trades)) {
            return [
                'planned_r_multiple' => 0,
                'realized_r_multiple' => 0,
                'avg_loss' => 0,
                'max_drawdown' => 0,
                'expectancy' => 0,
                'target_achievement_rate' => 0
            ];
        }

        $plannedRs = [];
        $realizedRs = [];
        $losses = [];
        $cumulativePnL = 0;
        $peak = 0;
        $maxDrawdown = 0;
        $targetAchieved = 0;
        $totalWithTargets = 0;

        foreach ($trades as $trade) {
            $entryPrice = floatval($trade['entry_price']);
            $exitPrice = floatval($trade['exit_price']);
            $stopLoss = floatval($trade['stop_loss']);
            $target = floatval($trade['target']);
            $pnl = floatval($trade['pnl_amount']);

            // Calculate planned R (risk-reward ratio)
            if ($stopLoss > 0 && $target > 0) {
                $risk = abs($entryPrice - $stopLoss);
                $reward = abs($target - $entryPrice);
                if ($risk > 0) {
                    $plannedRs[] = $reward / $risk;
                }

                $totalWithTargets++;
                // Check if target was achieved
                if (($entryPrice < $target && $exitPrice >= $target) ||
                    ($entryPrice > $target && $exitPrice <= $target)) {
                    $targetAchieved++;
                }
            }

            // Calculate realized R
            if ($stopLoss > 0) {
                $risk = abs($entryPrice - $stopLoss);
                if ($risk > 0) {
                    $realizedR = $pnl / $risk;
                    $realizedRs[] = $realizedR;
                }
            }

            // Track losses for average loss calculation
            if ($pnl < 0) {
                $losses[] = $pnl;
            }

            // Calculate drawdown
            $cumulativePnL += $pnl;
            if ($cumulativePnL > $peak) {
                $peak = $cumulativePnL;
            }
            $drawdown = $peak - $cumulativePnL;
            if ($drawdown > $maxDrawdown) {
                $maxDrawdown = $drawdown;
            }
        }

        $plannedR = count($plannedRs) > 0 ? round(array_sum($plannedRs) / count($plannedRs), 2) : 0;
        $realizedR = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;
        $avgLoss = count($losses) > 0 ? round(array_sum($losses) / count($losses), 2) : 0;
        $expectancy = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;
        $targetAchievementRate = $totalWithTargets > 0 ? round(($targetAchieved / $totalWithTargets) * 100, 1) : 0;

        return [
            'planned_r_multiple' => $plannedR,
            'realized_r_multiple' => $realizedR,
            'avg_loss' => $avgLoss,
            'max_drawdown' => round($maxDrawdown, 2),
            'expectancy' => $expectancy,
            'target_achievement_rate' => $targetAchievementRate,
            'total_trades_with_targets' => $totalWithTargets
        ];
    }

    private function getEmotionalStateData($userId, $fromDate, $db, $toDate = null)
    {
        try {
            // Get emotional state analysis
            $whereClause = "t.user_id = ? AND t.deleted_at IS NULL AND t.datetime >= ? AND t.market_type = '1'";
            $params = [$userId, $fromDate];

            if ($toDate) {
                $whereClause .= " AND t.datetime <= ?";
                $params[] = $toDate . ' 23:59:59';
            }

            $emotions = $db->query("
                SELECT
                    e.emotion,
                    COUNT(t.id) as trade_count,
                    AVG(t.pnl_amount) as avg_pnl,
                    AVG(t.confidence) as avg_confidence,
                    AVG(CASE WHEN t.rr_ratio > 0 THEN t.rr_ratio ELSE NULL END) as avg_rr
                FROM trades t
                LEFT JOIN emotions e ON t.emotion = e.id
                WHERE {$whereClause}
                GROUP BY t.emotion, e.emotion
                ORDER BY trade_count DESC
            ", $params)->getResultArray();
        } catch (Exception $e) {
            log_message('error', 'Emotional State Data Error: ' . $e->getMessage());
            return [
                'total_emotional_trades' => 0,
                'dominant_emotion' => 'Unknown',
                'dominant_emotion_performance' => 0,
                'emotions_breakdown' => []
            ];
        }

        $totalTrades = array_sum(array_column($emotions, 'trade_count'));
        $dominantEmotion = $emotions[0] ?? null;

        return [
            'total_emotional_trades' => $totalTrades,
            'dominant_emotion' => $dominantEmotion ? $dominantEmotion['emotion'] : 'Unknown',
            'dominant_emotion_performance' => $dominantEmotion ? round(floatval($dominantEmotion['avg_pnl']), 2) : 0,
            'emotions_breakdown' => array_slice(array_map(function($emotion) use ($totalTrades) {
                return [
                    'emotion' => $emotion['emotion'] ?? 'Unknown',
                    'count' => intval($emotion['trade_count']),
                    'percentage' => $totalTrades > 0 ? round((intval($emotion['trade_count']) / $totalTrades) * 100, 1) : 0,
                    'avg_pnl' => round(floatval($emotion['avg_pnl']), 2),
                    'avg_confidence' => round(floatval($emotion['avg_confidence']), 1),
                    'avg_rr' => round(floatval($emotion['avg_rr'] ?? 0), 2)
                ];
            }, $emotions), 0, 5)
        ];
    }

    private function getSymbolAnalysisData($userId, $fromDate, $db, $toDate = null)
    {
        // Get symbol frequency and performance
        $whereClause = "user_id = ? AND deleted_at IS NULL AND datetime >= ? AND market_type = '1' AND symbol IS NOT NULL";
        $params = [$userId, $fromDate];

        if ($toDate) {
            $whereClause .= " AND datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $symbols = $db->query("
            SELECT
                symbol,
                COUNT(*) as trade_count,
                SUM(pnl_amount) as total_pnl,
                AVG(pnl_amount) as avg_pnl,
                SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins,
                AVG(confidence) as avg_confidence
            FROM trades
            WHERE {$whereClause}
            GROUP BY symbol
            ORDER BY trade_count DESC
            LIMIT 10
        ", $params)->getResultArray();

        $totalSymbolTrades = array_sum(array_column($symbols, 'trade_count'));
        $mostTradedSymbol = $symbols[0] ?? null;

        return [
            'total_symbols_traded' => count($symbols),
            'most_traded_symbol' => $mostTradedSymbol ? $mostTradedSymbol['symbol'] : 'None',
            'most_traded_performance' => $mostTradedSymbol ? round(floatval($mostTradedSymbol['total_pnl']), 2) : 0,
            'symbol_concentration' => $mostTradedSymbol && $totalSymbolTrades > 0 ?
                round((intval($mostTradedSymbol['trade_count']) / $totalSymbolTrades) * 100, 1) : 0,
            'top_symbols' => array_slice(array_map(function($symbol) {
                $tradeCount = intval($symbol['trade_count']);
                $wins = intval($symbol['wins']);
                return [
                    'symbol' => $symbol['symbol'],
                    'trades' => $tradeCount,
                    'total_pnl' => round(floatval($symbol['total_pnl']), 2),
                    'avg_pnl' => round(floatval($symbol['avg_pnl']), 2),
                    'win_rate' => $tradeCount > 0 ? round(($wins / $tradeCount) * 100, 1) : 0,
                    'avg_confidence' => round(floatval($symbol['avg_confidence']), 1)
                ];
            }, $symbols), 0, 5)
        ];
    }

    private function getCapitalUsageData($userId, $fromDate, $db, $toDate = null)
    {
        // Get capital usage patterns
        $whereClause = "user_id = ? AND deleted_at IS NULL AND datetime >= ? AND market_type = '1' AND entry_amount IS NOT NULL AND entry_amount > 0";
        $params = [$userId, $fromDate];

        if ($toDate) {
            $whereClause .= " AND datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $trades = $db->query("
            SELECT
                entry_amount,
                pnl_amount
            FROM trades
            WHERE {$whereClause}
        ", $params)->getResultArray();

        if (empty($trades)) {
            return [
                'avg_capital_per_trade' => 0,
                'max_capital_used' => 0,
                'min_capital_used' => 0,
                'capital_efficiency' => 0
            ];
        }

        $amounts = array_column($trades, 'entry_amount');
        $pnls = array_column($trades, 'pnl_amount');

        $avgCapital = round(array_sum($amounts) / count($amounts), 2);
        $maxCapital = max($amounts);
        $minCapital = min($amounts);

        // Calculate capital efficiency (total PnL / total capital used)
        $totalCapitalUsed = array_sum($amounts);
        $totalPnL = array_sum($pnls);
        $capitalEfficiency = $totalCapitalUsed > 0 ? round(($totalPnL / $totalCapitalUsed) * 100, 2) : 0;

        return [
            'avg_capital_per_trade' => $avgCapital,
            'max_capital_used' => $maxCapital,
            'min_capital_used' => $minCapital,
            'capital_efficiency' => $capitalEfficiency,
            'total_capital_deployed' => $totalCapitalUsed
        ];
    }

    private function getTradingPatternsData($userId, $fromDate, $db, $toDate = null)
    {
        // Get trading frequency and patterns
        $whereClause = "user_id = ? AND deleted_at IS NULL AND datetime >= ? AND market_type = '1'";
        $params = [$userId, $fromDate];

        if ($toDate) {
            $whereClause .= " AND datetime <= ?";
            $params[] = $toDate . ' 23:59:59';
        }

        $patterns = $db->query("
            SELECT
                DATE(datetime) as trade_date,
                DAYNAME(datetime) as day_name,
                COUNT(*) as trades_count,
                SUM(pnl_amount) as daily_pnl,
                SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as daily_wins,
                SUM(CASE WHEN pnl_amount < 0 THEN 1 ELSE 0 END) as daily_losses
            FROM trades
            WHERE {$whereClause}
            GROUP BY DATE(datetime), DAYNAME(datetime)
            ORDER BY trade_date DESC
        ", $params)->getResultArray();

        if (empty($patterns)) {
            return [
                'avg_trades_per_day' => 0,
                'max_trades_per_day' => 0,
                'overtrading_days' => 0,
                'best_trading_day' => 'None',
                'consecutive_winning_days' => 0,
                'consecutive_losing_days' => 0
            ];
        }

        $tradeCounts = array_column($patterns, 'trades_count');
        $avgTradesPerDay = round(array_sum($tradeCounts) / count($tradeCounts), 1);
        $maxTradesPerDay = max($tradeCounts);
        $overtradingDays = count(array_filter($tradeCounts, fn($count) => $count > 7));

        // Find best performing day of week
        $dayPerformance = [];
        foreach ($patterns as $pattern) {
            $dayName = $pattern['day_name'];
            if (!isset($dayPerformance[$dayName])) {
                $dayPerformance[$dayName] = ['total_pnl' => 0, 'count' => 0];
            }
            $dayPerformance[$dayName]['total_pnl'] += floatval($pattern['daily_pnl']);
            $dayPerformance[$dayName]['count']++;
        }

        $bestDay = 'None';
        $bestDayPnL = PHP_FLOAT_MIN;
        foreach ($dayPerformance as $day => $data) {
            $avgPnL = $data['count'] > 0 ? $data['total_pnl'] / $data['count'] : 0;
            if ($avgPnL > $bestDayPnL) {
                $bestDayPnL = $avgPnL;
                $bestDay = $day;
            }
        }

        // Calculate consecutive streaks
        $consecutiveWins = 0;
        $consecutiveLosses = 0;
        $currentWinStreak = 0;
        $currentLossStreak = 0;

        foreach (array_reverse($patterns) as $pattern) {
            $dailyPnL = floatval($pattern['daily_pnl']);
            if ($dailyPnL > 0) {
                $currentWinStreak++;
                $currentLossStreak = 0;
                $consecutiveWins = max($consecutiveWins, $currentWinStreak);
            } elseif ($dailyPnL < 0) {
                $currentLossStreak++;
                $currentWinStreak = 0;
                $consecutiveLosses = max($consecutiveLosses, $currentLossStreak);
            } else {
                $currentWinStreak = 0;
                $currentLossStreak = 0;
            }
        }

        return [
            'avg_trades_per_day' => $avgTradesPerDay,
            'max_trades_per_day' => $maxTradesPerDay,
            'overtrading_days' => $overtradingDays,
            'best_trading_day' => $bestDay,
            'consecutive_winning_days' => $consecutiveWins,
            'consecutive_losing_days' => $consecutiveLosses,
            'total_trading_days' => count($patterns)
        ];
    }
}
