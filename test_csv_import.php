<?php
/**
 * Test script for CSV-only Trade Import functionality
 */

require_once 'app/Libraries/TradeImporter.php';

use App\Libraries\TradeImporter;

echo "=== CSV-Only Trade Import Test ===\n\n";

$importer = new TradeImporter();

// Test CSV file
$testFile = 'test_data/zerodha_sample.csv';

echo "Testing: Zerodha CSV Sample\n";
echo "File: $testFile\n";

if (!file_exists($testFile)) {
    echo "❌ File not found: $testFile\n";
    echo "Creating sample file...\n";
    
    // Create test directory if it doesn't exist
    if (!is_dir('test_data')) {
        mkdir('test_data', 0777, true);
    }
    
    // Create sample CSV content
    $csvContent = "tradingsymbol,order_execution_time,transaction_type,quantity,average_price,net_pnl\n";
    $csvContent .= "RELIANCE,2024-01-15 09:30:00,BUY,100,2450.50,\n";
    $csvContent .= "RELIANCE,2024-01-15 14:30:00,SELL,100,2465.75,1525.00\n";
    $csvContent .= "INFY,2024-01-16 10:15:00,BUY,50,1580.25,\n";
    $csvContent .= "INFY,2024-01-16 15:45:00,SELL,50,1575.80,-222.50\n";
    
    file_put_contents($testFile, $csvContent);
    echo "✅ Sample file created\n";
}

try {
    // Parse file
    $parsed = $importer->parseFile($testFile);
    echo "✅ CSV file parsed successfully\n";
    echo "   Headers: " . implode(', ', $parsed['headers']) . "\n";
    echo "   Rows: " . count($parsed['data']) . "\n";
    
    // Detect broker format
    $brokerType = $importer->detectBrokerFormat($parsed['headers']);
    echo "   Detected broker: $brokerType\n";
    
    // Test column mapping
    $mapping = $importer->mapColumns($parsed['headers'], $brokerType);
    echo "   Column mapping:\n";
    foreach ($mapping as $field => $column) {
        echo "     $field -> " . ($column ?: 'NOT MAPPED') . "\n";
    }
    
    // Test data validation
    $validation = $importer->validateTradeData($parsed['data'], $mapping);
    echo "   Valid trades: " . count($validation['valid']) . "\n";
    echo "   Errors: " . count($validation['errors']) . "\n";
    
    if (!empty($validation['errors'])) {
        echo "   Error details:\n";
        foreach (array_slice($validation['errors'], 0, 3) as $error) {
            echo "     Row {$error['row']}: " . implode(', ', $error['errors']) . "\n";
        }
    }
    
    // Show sample valid trade data
    if (!empty($validation['valid'])) {
        echo "   Sample valid trade:\n";
        $sample = $validation['valid'][0];
        foreach ($sample as $key => $value) {
            echo "     $key: $value\n";
        }
    }
    
    echo "\n✅ CSV Import functionality is working correctly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "You can now use the Import Trades button in your application!\n";
?>
