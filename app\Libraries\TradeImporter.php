<?php

namespace App\Libraries;

use CodeIgniter\Files\File;

class TradeImporter
{
    protected $supportedFormats = ['csv'];
    protected $maxFileSize = 10485760; // 10MB
    
    // Common broker column mappings
    protected $brokerMappings = [
        'zerodha' => [
            'symbol' => ['symbol', 'tradingsymbol', 'instrument'],
            'datetime' => ['trade_date', 'date', 'order_execution_time'],
            'trade_type' => ['transaction_type', 'side', 'buy_sell'],
            'entry_price' => ['price', 'average_price', 'avg_price'],
            'exit_price' => ['price', 'average_price', 'avg_price'],
            'entry_quantity' => ['quantity', 'qty', 'filled_quantity'],
            'pnl_amount' => ['pnl', 'net_pnl', 'realized_pnl']
        ],
        'angel_one' => [
            'symbol' => ['trading_symbol', 'symbol', 'instrument_name'],
            'datetime' => ['trade_date', 'order_date', 'execution_time'],
            'trade_type' => ['transaction_type', 'side', 'buy_sell'],
            'entry_price' => ['price', 'execution_price', 'avg_price'],
            'exit_price' => ['price', 'execution_price', 'avg_price'],
            'entry_quantity' => ['quantity', 'filled_qty', 'executed_qty'],
            'pnl_amount' => ['pnl', 'net_pnl', 'profit_loss']
        ],
        'icici_direct' => [
            'symbol' => ['symbol', 'script_name', 'security'],
            'datetime' => ['trade_date', 'settlement_date', 'order_date'],
            'trade_type' => ['buy_sell', 'transaction_type', 'side'],
            'entry_price' => ['rate', 'price', 'execution_price'],
            'exit_price' => ['rate', 'price', 'execution_price'],
            'entry_quantity' => ['quantity', 'qty', 'shares'],
            'pnl_amount' => ['pnl', 'profit_loss', 'net_amount']
        ]
    ];

    public function validateFile(File $file): array
    {
        $errors = [];
        
        // Check file size
        if ($file->getSize() > $this->maxFileSize) {
            $errors[] = 'File size exceeds 10MB limit';
        }
        
        // Check file extension
        $extension = strtolower($file->getExtension());
        if (!in_array($extension, $this->supportedFormats)) {
            $errors[] = 'Unsupported file format. Please use CSV files only';
        }
        
        // Check if file is readable
        if (!$file->isValid()) {
            $errors[] = 'File is corrupted or unreadable';
        }
        
        return $errors;
    }

    public function parseFile(string $filePath): array
    {
        try {
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            if ($extension === 'csv') {
                return $this->parseCsv($filePath);
            } else {
                throw new \Exception('Only CSV files are supported in this version');
            }
        } catch (\Exception $e) {
            throw new \Exception('Failed to parse file: ' . $e->getMessage());
        }
    }

    protected function parseCsv(string $filePath): array
    {
        $data = [];
        $headers = [];
        
        if (($handle = fopen($filePath, 'r')) !== false) {
            $rowIndex = 0;
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                if ($rowIndex === 0) {
                    $headers = array_map('trim', $row);
                } else {
                    $data[] = array_combine($headers, array_map('trim', $row));
                }
                $rowIndex++;
            }
            fclose($handle);
        }
        
        return ['headers' => $headers, 'data' => $data];
    }

    // Excel parsing removed - CSV only version

    public function detectBrokerFormat(array $headers): string
    {
        $headerLower = array_map('strtolower', $headers);
        
        // Check for Zerodha patterns
        if (in_array('tradingsymbol', $headerLower) || in_array('order_execution_time', $headerLower)) {
            return 'zerodha';
        }
        
        // Check for Angel One patterns
        if (in_array('trading_symbol', $headerLower) || in_array('angel_client_id', $headerLower)) {
            return 'angel_one';
        }
        
        // Check for ICICI Direct patterns
        if (in_array('script_name', $headerLower) || in_array('settlement_date', $headerLower)) {
            return 'icici_direct';
        }
        
        return 'generic';
    }

    public function mapColumns(array $headers, string $brokerType = 'generic'): array
    {
        $mapping = [];
        $headerLower = array_map('strtolower', $headers);
        
        $targetFields = [
            'symbol', 'datetime', 'trade_type', 'entry_price', 
            'exit_price', 'entry_quantity', 'pnl_amount'
        ];
        
        foreach ($targetFields as $field) {
            $mapping[$field] = $this->findBestMatch($field, $headerLower, $brokerType);
        }
        
        return $mapping;
    }

    protected function findBestMatch(string $targetField, array $headers, string $brokerType): ?string
    {
        // Try broker-specific mapping first
        if (isset($this->brokerMappings[$brokerType][$targetField])) {
            foreach ($this->brokerMappings[$brokerType][$targetField] as $pattern) {
                $index = array_search(strtolower($pattern), $headers);
                if ($index !== false) {
                    return $headers[$index];
                }
            }
        }
        
        // Fallback to generic patterns
        $genericPatterns = [
            'symbol' => ['symbol', 'instrument', 'stock', 'security', 'script'],
            'datetime' => ['date', 'time', 'timestamp', 'trade_date', 'order_date'],
            'trade_type' => ['type', 'side', 'buy_sell', 'transaction', 'direction'],
            'entry_price' => ['price', 'rate', 'entry', 'buy_price', 'avg_price'],
            'exit_price' => ['price', 'rate', 'exit', 'sell_price', 'avg_price'],
            'entry_quantity' => ['quantity', 'qty', 'shares', 'units', 'volume'],
            'pnl_amount' => ['pnl', 'profit', 'loss', 'net', 'amount']
        ];
        
        if (isset($genericPatterns[$targetField])) {
            foreach ($genericPatterns[$targetField] as $pattern) {
                foreach ($headers as $header) {
                    if (strpos(strtolower($header), $pattern) !== false) {
                        return $header;
                    }
                }
            }
        }
        
        return null;
    }

    public function validateTradeData(array $data, array $mapping): array
    {
        $errors = [];
        $validTrades = [];
        
        foreach ($data as $index => $row) {
            $rowErrors = [];
            $trade = [];
            
            // Validate required fields
            if (empty($row[$mapping['symbol']] ?? '')) {
                $rowErrors[] = 'Symbol is required';
            } else {
                $trade['symbol'] = strtoupper(trim($row[$mapping['symbol']]));
            }
            
            if (empty($row[$mapping['datetime']] ?? '')) {
                $rowErrors[] = 'Date is required';
            } else {
                $dateValue = $this->parseDate($row[$mapping['datetime']]);
                if (!$dateValue) {
                    $rowErrors[] = 'Invalid date format';
                } else {
                    $trade['datetime'] = $dateValue;
                }
            }
            
            // Parse trade type
            if (!empty($row[$mapping['trade_type']] ?? '')) {
                $tradeType = $this->parseTradeType($row[$mapping['trade_type']]);
                if ($tradeType) {
                    $trade['trade_type'] = $tradeType;
                } else {
                    $rowErrors[] = 'Invalid trade type';
                }
            }
            
            // Parse numeric fields
            $numericFields = ['entry_price', 'exit_price', 'entry_quantity', 'pnl_amount'];
            foreach ($numericFields as $field) {
                if (!empty($row[$mapping[$field]] ?? '')) {
                    $value = $this->parseNumeric($row[$mapping[$field]]);
                    if ($value !== null) {
                        $trade[$field] = $value;
                    }
                }
            }
            
            if (empty($rowErrors)) {
                $validTrades[] = $trade;
            } else {
                $errors[] = [
                    'row' => $index + 2, // +2 because index starts at 0 and we skip header
                    'errors' => $rowErrors,
                    'data' => $row
                ];
            }
        }
        
        return ['valid' => $validTrades, 'errors' => $errors];
    }

    protected function parseDate(string $dateString): ?string
    {
        $formats = [
            'Y-m-d H:i:s',
            'Y-m-d',
            'd-m-Y',
            'd/m/Y',
            'm/d/Y',
            'Y/m/d',
            'd-M-Y',
            'M d, Y'
        ];
        
        foreach ($formats as $format) {
            $date = \DateTime::createFromFormat($format, trim($dateString));
            if ($date !== false) {
                return $date->format('Y-m-d H:i:s');
            }
        }
        
        return null;
    }

    protected function parseTradeType(string $typeString): ?int
    {
        $type = strtolower(trim($typeString));
        
        if (in_array($type, ['buy', 'long', 'b', '1'])) {
            return 1;
        } elseif (in_array($type, ['sell', 'short', 's', '2'])) {
            return 2;
        }
        
        return null;
    }

    protected function parseNumeric(string $value): ?float
    {
        // Remove currency symbols and commas
        $cleaned = preg_replace('/[₹$,\s]/', '', trim($value));
        
        if (is_numeric($cleaned)) {
            return (float)$cleaned;
        }
        
        return null;
    }
}
