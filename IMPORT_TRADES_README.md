# Trade Import Functionality

This document explains how to use the CSV/Excel import feature for bulk importing trade data from broker files.

## Features

- **File Support**: CSV (.csv) and Excel (.xlsx, .xls) files up to 10MB
- **Broker Support**: Auto-detection for Zerodha, Angel One, ICICI Direct, and generic formats
- **Column Mapping**: Automatic column detection with manual override capability
- **Data Validation**: Validates required fields and data formats before import
- **Duplicate Detection**: Skips duplicate trades based on symbol + datetime + user_id
- **Progress Tracking**: Shows import progress and detailed results

## How to Use

### 1. Access Import Feature
- Navigate to the Trades page
- Click the "Import Trades" button in the toolbar (next to Sync broker and New Trade buttons)

### 2. Upload File
- Drag and drop your CSV/Excel file or click to browse
- Supported formats: CSV, XLSX, XLS (max 10MB)
- The system will automatically parse and preview your data

### 3. Column Mapping
- Review the auto-detected column mappings
- Adjust mappings if needed using the dropdown selectors
- Required fields: Symbol and Date/Time
- Optional fields: Trade Type, Entry Price, Exit Price, Quantity, P&L Amount

### 4. Import Process
- Click "Import Trades" to start processing
- The system will validate data and import valid trades
- Duplicate trades (same symbol + date + user) will be skipped
- View detailed import results including success/error counts

## Supported Broker Formats

### Zerodha
Expected columns:
- `tradingsymbol` or `symbol` → Symbol
- `order_execution_time` or `trade_date` → Date/Time
- `transaction_type` → Trade Type (BUY/SELL)
- `quantity` or `filled_quantity` → Quantity
- `average_price` or `price` → Entry/Exit Price
- `net_pnl` or `pnl` → P&L Amount

### Angel One
Expected columns:
- `trading_symbol` or `symbol` → Symbol
- `trade_date` or `order_date` → Date/Time
- `transaction_type` → Trade Type (BUY/SELL)
- `filled_qty` or `quantity` → Quantity
- `execution_price` or `price` → Entry/Exit Price
- `profit_loss` or `pnl` → P&L Amount

### ICICI Direct
Expected columns:
- `script_name` or `symbol` → Symbol
- `settlement_date` or `trade_date` → Date/Time
- `buy_sell` → Trade Type (BUY/SELL)
- `shares` or `quantity` → Quantity
- `rate` or `price` → Entry/Exit Price
- `net_amount` or `pnl` → P&L Amount

### Generic Format
For other brokers, ensure your file has columns that can be mapped to:
- Symbol (instrument name)
- Date/Time (trade execution date)
- Trade Type (BUY/SELL or Long/Short)
- Quantity (number of shares/units)
- Price (entry/exit price)
- P&L (profit/loss amount)

## Data Processing

### Auto-Calculations
The system automatically calculates:
- **Entry Amount**: entry_price × entry_quantity
- **P&L Percentage**: (pnl_amount / entry_amount) × 100
- **Risk-Reward Ratio**: Based on entry, exit, and stop loss prices

### Default Values
Missing optional fields are set to defaults:
- **Market Type**: Indian (1)
- **Currency**: ₹ (Rupees)
- **Strategy**: Default strategy (ID: 1)
- **Outcome**: Default outcome (ID: 1)
- **Emotion**: Default emotion (ID: 1)
- **Confidence**: 5 (medium)
- **Satisfaction**: 5 (medium)
- **Broker**: "imported"

### Validation Rules
- **Symbol**: Required, converted to uppercase
- **Date/Time**: Required, supports multiple date formats
- **Trade Type**: BUY/Long = 1, SELL/Short = 2
- **Numeric Fields**: Prices, quantities, and P&L must be valid numbers
- **Duplicates**: Trades with same symbol + datetime + user are skipped

## Error Handling

### Common Errors
- **Invalid file format**: Only CSV and Excel files are supported
- **File too large**: Maximum 10MB file size
- **Missing required columns**: Symbol and Date/Time are mandatory
- **Invalid date format**: Date must be in recognizable format
- **Invalid trade type**: Must be BUY/SELL or Long/Short
- **Duplicate trades**: Same symbol + date + user already exists

### Error Resolution
- Check file format and size
- Ensure required columns are present and properly named
- Verify date formats (YYYY-MM-DD, DD/MM/YYYY, etc.)
- Use standard trade type values (BUY, SELL, Long, Short)
- Remove or update duplicate entries

## Sample Files

Sample CSV files are provided in the `test_data/` directory:
- `zerodha_sample.csv` - Zerodha format example
- `angel_one_sample.csv` - Angel One format example
- `icici_direct_sample.csv` - ICICI Direct format example

## Technical Details

### File Processing
- Uses PhpSpreadsheet library for Excel file parsing
- CSV files are parsed using native PHP functions
- Temporary files are stored in `writable/uploads/temp/` and cleaned up after processing

### Database Integration
- Imports data into the existing `trades` table
- Maintains referential integrity with strategies, outcomes, and emotions tables
- Uses soft deletes and timestamps for data consistency

### Security
- File type validation prevents malicious uploads
- File size limits prevent resource exhaustion
- User authentication required for all import operations
- Temporary files are automatically cleaned up

## Troubleshooting

### Import Fails
1. Check file format (CSV/Excel only)
2. Verify file size (under 10MB)
3. Ensure required columns are present
4. Check date formats in your data

### No Data Imported
1. Verify column mappings are correct
2. Check for validation errors in the results
3. Ensure data formats match expected types
4. Look for duplicate entries being skipped

### Partial Import
1. Review error details in import results
2. Fix data issues in source file
3. Re-import with corrected data
4. Check for missing required fields

For additional support, check the browser console for detailed error messages during the import process.
