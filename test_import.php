<?php
/**
 * Test script for Trade Import functionality
 * Run this script to test the TradeImporter library
 */

require_once 'vendor/autoload.php';
require_once 'app/Libraries/TradeImporter.php';

use App\Libraries\TradeImporter;

echo "=== Trade Import Functionality Test ===\n\n";

$importer = new TradeImporter();

// Test files
$testFiles = [
    'test_data/zerodha_sample.csv' => 'Zerodha CSV',
    'test_data/angel_one_sample.csv' => 'Angel One CSV',
    'test_data/icici_direct_sample.csv' => 'ICICI Direct CSV'
];

foreach ($testFiles as $filePath => $description) {
    echo "Testing: $description\n";
    echo "File: $filePath\n";
    
    if (!file_exists($filePath)) {
        echo "❌ File not found: $filePath\n\n";
        continue;
    }
    
    try {
        // Parse file
        $parsed = $importer->parseFile($filePath);
        echo "✅ File parsed successfully\n";
        echo "   Headers: " . implode(', ', $parsed['headers']) . "\n";
        echo "   Rows: " . count($parsed['data']) . "\n";
        
        // Detect broker format
        $brokerType = $importer->detectBrokerFormat($parsed['headers']);
        echo "   Detected broker: $brokerType\n";
        
        // Test column mapping
        $mapping = $importer->mapColumns($parsed['headers'], $brokerType);
        echo "   Column mapping:\n";
        foreach ($mapping as $field => $column) {
            echo "     $field -> " . ($column ?: 'NOT MAPPED') . "\n";
        }
        
        // Test data validation
        $validation = $importer->validateTradeData($parsed['data'], $mapping);
        echo "   Valid trades: " . count($validation['valid']) . "\n";
        echo "   Errors: " . count($validation['errors']) . "\n";
        
        if (!empty($validation['errors'])) {
            echo "   Error details:\n";
            foreach (array_slice($validation['errors'], 0, 3) as $error) {
                echo "     Row {$error['row']}: " . implode(', ', $error['errors']) . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat('-', 50) . "\n\n";
}

echo "=== Test Complete ===\n";
?>
