<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// $routes->add('/Landing', 'LandingController::index');
$routes->add('/Login', 'Home::Login');
$routes->add('/contact-us', 'LandingController::ContactUs');
$routes->add('/privacy-policy', 'LandingController::PrivacyPolicy');
$routes->add('/about-us', 'LandingController::AboutUs');
$routes->add('/disclaimer', 'LandingController::Disclaimer');
$routes->add('/refund-and-cancellation', 'LandingController::Refund');
$routes->add('/disclosures', 'LandingController::Disclosures');
$routes->add('/terms-and-conditions', 'LandingController::Terms');
$routes->add('/shipping-policy', 'LandingController::Shipping');


// $routes->add('/', 'Home::Login');
$routes->add('/', 'LandingController::index');
$routes->add('/r/(:any)', 'LandingController::index');
// $routes->add('/testlogin', 'Home::LoginX');
$routes->add('/signup', 'Home::signup');
$routes->add('/signin', 'Home::signin');
$routes->add('/logout', 'Home::logout');
$routes->get('/verify-email/(:any)', 'Home::verifyEmail/$1');
$routes->add('/dashboard', 'Home::index');
$routes->add('/saveTrade', 'Home::saveTrade');

$routes->add('/googleCallback', 'Auth::googleCallback');
$routes->add('/googleLogin', 'Auth::googleLogin');

$routes->get('ajax-trades', 'Home::ajaxTrades');

$routes->add('myTrades', 'Home::myTrades');
$routes->add('deleteTrade', 'Home::deleteTrade');
$routes->add('viewTrade', 'Home::viewTrade');
$routes->add('getEditTradeData', 'Home::getEditTradeData');
$routes->post('importTrades', 'Home::importTrades');
$routes->post('processImport', 'Home::processImport');

$routes->add('getDashboardMetrics', 'Home::getDashboardMetrics');
$routes->add('getEquityChartData', 'Home::getEquityChartData');
$routes->add('fetchTopTrades', 'Home::fetchTopTrades');
$routes->add('getWinLossChartData', 'Home::getWinLossChartData');
$routes->add('getMonthlyConfidenceScore', 'Home::getMonthlyConfidenceScore');
$routes->add('getStrategyPerformance', 'Home::getStrategyPerformance');
$routes->add('getDailyPnl', 'Home::getDailyPnl');
$routes->add('getStrategyPnls', 'Home::getStrategyPnls');
$routes->add('getMistakesPieData', 'Home::getMistakesPieData');
$routes->add('Tutorials', 'Home::Tutorials');


$routes->add('Reports', 'Reports::Reports');
$routes->add('getTradePerformance', 'Reports::getTradePerformance');
$routes->add('getDailyPerformance', 'Reports::getDailyPerformance');
$routes->add('getTimeMetrics', 'Reports::getTimeMetrics');
$routes->add('getRiskManagementMetrics', 'Reports::getRiskManagementMetrics');
$routes->add('getEmotionalStateMetrics', 'Reports::getEmotionalStateMetrics');
$routes->add('getTargetOutcomeMetrics', 'Reports::getTargetOutcomeMetrics');
$routes->add('getSetupEffectiveness', 'Reports::getSetupEffectiveness');
$routes->add('getSymbolFrequency', 'Reports::getSymbolFrequency');
$routes->add('getAvgRRByEmotion', 'Reports::getAvgRRByEmotion');
$routes->add('getTradingOutcomes', 'Reports::getTradingOutcomes');
$routes->add('getTradesPerDay', 'Reports::getTradesPerDay');
$routes->add('getQuantityAnalysis', 'Reports::getQuantityAnalysis');
$routes->add('getCapitalUsage', 'Reports::getCapitalUsage');
$routes->add('getDailyTradeActivity', 'Reports::getDailyTradeActivity');
$routes->add('getTradeExecution', 'Reports::getTradeExecution');
$routes->post('get-weekday-performance', 'Reports::getWeekdayPerformance');

$routes->add('/Calender', 'Home::Calender');
$routes->get('getTradesByMonth', 'Home::getTradesByMonth');

$routes->get('Tools', 'Home::Tools');
$routes->get('Calculator', 'Home::Calculator');
$routes->get('ReturnsCalculator', 'Home::ReturnsCalculator');

$routes->get('Goals', 'Goals::index');
$routes->add('submitPnlGoalForm', 'Goals::submitPnlGoalForm');
$routes->add('updateGoalStatuses', 'Goals::updateGoalStatuses');
$routes->add('getPnlGoalDashboardStats', 'Goals::getPnlGoalDashboardStats');
$routes->add('getActiveGoals', 'Goals::getActiveGoals');

$routes->get('Strategy', 'Strategy::index');
$routes->add('fetchStrategyPerformance', 'Strategy::fetchStrategyCards');
$routes->add('recentTrades', 'Strategy::recentTrades');
$routes->add('getStrategyDetails', 'Strategy::getStrategyDetails');
$routes->add('saveUserStrategy', 'Strategy::saveUserStrategy');
$routes->add('getEditStrategyData', 'Strategy::getEditStrategyData');
$routes->add('updateStrategy', 'Strategy::updateStrategy');

$routes->get('Challenge', 'ChallengeController::index');
$routes->get('getChallengeData', 'ChallengeController::getChallengeData');
$routes->get('getChallengeEquityData', 'ChallengeController::getChallengeEquityData');
$routes->add('saveChallengeData', 'ChallengeController::saveChallengeData');
$routes->post('getProgressData', 'ChallengeController::getProgressData');
$routes->post('getChallengeMeta', 'ChallengeController::getChallengeMeta');
$routes->post('getChallengeStats', 'ChallengeController::getChallengeStats');
$routes->post('getChallengeStatsV2', 'ChallengeController::getChallengeStatsV2');
$routes->post('getConfidenceLevel', 'ChallengeController::getConfidenceLevel');
$routes->post('getChallengeTradesPaginated', 'ChallengeController::getChallengeTradesPaginated');
$routes->post('getTopTrades', 'ChallengeController::getTopTrades');
$routes->post('getEquityCurve', 'ChallengeController::getEquityCurve');
$routes->post('getMostTradedSymbols', 'ChallengeController::getMostTradedSymbols');

// Rules routes
$routes->get('Rules', 'RulesController::index');
$routes->get('getRules', 'RulesController::getRules');
$routes->get('getRulesWithStats', 'RulesController::getRulesWithStats');
$routes->get('getRule/(:num)', 'RulesController::getRule/$1');
$routes->get('testRules', 'RulesController::testRules');
$routes->post('createRule', 'RulesController::createRule');
$routes->post('updateRule/(:num)', 'RulesController::updateRule/$1');
$routes->delete('deleteRule/(:num)', 'RulesController::deleteRule/$1');
$routes->post('deleteRule/(:num)', 'RulesController::deleteRule/$1'); // Add POST method as fallback
$routes->post('getRulesAnalytics', 'RulesController::getRulesAnalytics');

// Mistake routes
$routes->get('Mistakes', 'Mistake::index');
$routes->get('getMistakeMetrics', 'Mistake::getMistakeMetrics');
$routes->get('getMistakeDistribution', 'Mistake::getMistakeDistribution');
$routes->get('getRecentMistakes', 'Mistake::getRecentMistakes');
$routes->get('getMistakeDetails', 'Mistake::getMistakeDetails');
$routes->get('getMistakeHeatmap', 'Mistake::getMistakeHeatmap');

// AI Test routes
$routes->get('ai-test', 'AITestController::index');
$routes->post('ai-test/analyze', 'AITestController::analyzeReports');
$routes->get('ai-test/debug', 'AITestController::debugAPI');
$routes->get('ai-test/test', 'AITestController::testAPI');

// Custom mistake routes
$routes->get('getCustomMistakes', 'Mistake::getCustomMistakes');
$routes->post('createCustomMistake', 'Mistake::createCustomMistake');
$routes->post('updateCustomMistake/(:num)', 'Mistake::updateCustomMistake/$1');
$routes->delete('deleteCustomMistake/(:num)', 'Mistake::deleteCustomMistake/$1');
$routes->post('deleteCustomMistake/(:num)', 'Mistake::deleteCustomMistake/$1'); // POST fallback


$routes->add('MyProfile', 'Auth::MyProfile');
$routes->add('updateProfile', 'Auth::updateProfile');
$routes->add('saveBrokerDetails', 'Auth::saveBrokerDetails');
$routes->add('fetchConnectedBroker', 'Auth::fetchConnectedBroker');
$routes->add('deleteBroker', 'Auth::deleteBroker');
$routes->add('getDhanTradesFromPython', 'Auth::getDhanTradesFromPython');
$routes->add('syncTrades', 'Auth::syncTrades');


$routes->add('fetchCurrentUserTrades', 'TradeFetcher::fetchCurrentUserTrades');

// Community routes
$routes->get('community', 'CommunityController::index');
$routes->get('community/profile', 'CommunityController::profile');
$routes->post('community/updateProfile', 'CommunityController::updateProfile');
$routes->get('community/getFollowers', 'CommunityController::getFollowers');
$routes->get('community/getFollowing', 'CommunityController::getFollowing');
$routes->get('community/getPosts', 'CommunityController::getPosts');
$routes->post('community/createPost', 'CommunityController::createPost');
$routes->post('community/toggleLike', 'CommunityController::toggleLike');
$routes->get('community/testLike', 'CommunityController::testLike');
$routes->post('community/addComment', 'CommunityController::addComment');
$routes->get('community/getComments/(:num)', 'CommunityController::getComments/$1');
$routes->post('community/toggleFollow', 'CommunityController::toggleFollow');
$routes->get('community/getMyPosts', 'CommunityController::getMyPosts');
$routes->match(['post', 'delete'], 'community/deletePost/(:num)', 'CommunityController::deletePost/$1');
$routes->get('community/searchPosts', 'CommunityController::searchPosts');
$routes->get('community/getNotifications', 'CommunityController::getNotifications');
$routes->post('community/markNotificationRead/(:num)', 'CommunityController::markNotificationRead/$1');
$routes->post('community/markAllNotificationsRead', 'CommunityController::markAllNotificationsRead');
$routes->get('community/getUnreadNotificationCount', 'CommunityController::getUnreadNotificationCount');
$routes->get('community/testUpload', 'CommunityController::testUpload');
$routes->get('community/fixImageUrls', 'CommunityController::fixImageUrls');
$routes->get('community/debugImages', 'CommunityController::debugImages');
$routes->get('community/image/(:segment)', 'CommunityController::serveImage/$1');

// payments
$routes->add('/createOrder', 'PaymentController::createOrder');
$routes->add('/saveTransaction', 'PaymentController::saveTransaction');

// Affiliates
$routes->add('/AffiliateDashboard', 'Affiliate::index');
$routes->add('/SalesDashboard', 'Affiliate::Sales');
$routes->add('/fetchReferralList', 'Affiliate::referrals');
$routes->add('/PayoutDetails', 'Affiliate::PayoutDetails');
$routes->add('/savePayoutDetails', 'Affiliate::saveBank');
$routes->add('/getBankDetails', 'Affiliate::getBankDetails');
$routes->add('/requestWithdraw', 'Affiliate::requestWithdraw');
$routes->add('/getWalletStats', 'Affiliate::getWalletStats');
$routes->add('/fetchPayouts', 'Affiliate::fetchPayouts');
$routes->add('/AffiliateFAQ', 'Affiliate::AffiliateFAQ');

// kite connect
$routes->get('sync-trades', 'KiteController::syncTrades');

$routes->get('kite/kite-login', 'KiteController::kiteLogin');
$routes->post('kite/save-access-token', 'KiteController::saveAccessToken');
$routes->get('zerodha/callback', 'KiteController::kiteCallback');
$routes->add('generateKiteToken', 'Auth::generateKiteToken');