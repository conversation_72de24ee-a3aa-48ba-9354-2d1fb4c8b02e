<?php

namespace App\Models;

use CodeIgniter\Model;

class TradeModel extends Model
{
    protected $table = 'trades';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'user_id',
        'symbol',
        'market_type',
        'currency',
        'datetime',
        'entry_price',
        'entry_quantity',
        'entry_amount',
        'exit_price',
        'pnl_amount',
        'pnl_percent',
        'trade_type',
        'stop_loss',
        'target',
        'strategy',
        'outcome',
        'rationale',
        'confidence',
        'satisfaction',
        'emotion',
        'lesson',
        'rr_ratio',
        'mistake_id',
        'broker',
        'broker_order_id'
    ];
}

?>