<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
        <div
            class="flex flex-col md:flex-row justify-between items-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
            <div>
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Trade History</h2>
                <!-- <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Last 30 days activity</p> -->
            </div>
            <div class="flex space-x-3 mt-4 md:mt-0">
                <div class="dropdown">
                    <div class="relative">
                        <select id="sortSelect"
                            class="appearance-none px-3 py-2 pr-10 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 w-full">
                            <option value="">Default</option>
                            <option value="pnl-desc">Highest PnL</option>
                            <option value="pnl-asc">Lowest PnL</option>
                            <option value="rr-desc">Highest R:R</option>
                            <option value="rr-asc">Lowest R:R</option>
                            <option value="date-desc">Newest First</option>
                            <option value="date-asc">Oldest First</option>
                        </select>
                        <i
                            class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none"></i>
                    </div>
                </div>
                <!-- Filter Button -->
                <button id="openModal"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2">
                    <i class="fas fa-filter"></i>
                    <span class="hidden sm:inline">Filter Trades</span>
                </button>

                <button id="fetchBrokerTrades"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2">
                    <i class="fas fa-sync-alt"></i>
                    <span class="hidden sm:inline">Sync broker</span>
                </button>

                <button id="importTradesBtn"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2">
                    <i class="fas fa-file-import"></i>
                    <span class="hidden sm:inline">Import Trades</span>
                </button>

                <button id="addTradeBtn"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2">
                    <i class="fas fa-plus"></i>
                    <span class="hidden sm:inline">New Trade</span>
                </button>

            </div>
        </div>

        <div class="trade-table-container">
            <table class="w-full trade-table">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <!-- <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Trade ID</th> -->
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Date</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Symbol</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Direction</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Entry/Exit</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            P/L ( / %)</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Risk/Reward</th>
                        <th
                            class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Strategy</th>
                        <th
                            class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Outcome</th>
                        <th
                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">

                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div
            class="px-6 py-4 border-t dark:border-gray-700 flex flex-col md:flex-row items-center justify-between gap-4">
            <div class="text-sm text-gray-500 dark:text-gray-400 pagination-summary">
                <!-- Filled dynamically -->
            </div>
            <div class="pagination-container flex space-x-1"></div>
        </div>
    </div>

    <div id="modalBackdrop"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden modal-container">
        <!-- Modal Container -->
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto fade-in modal-content">
            <!-- Modal Header -->
            <div
                class="sticky top-0 bg-white dark:bg-gray-800 border-b dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">Filter Trades</h3>
                <button id="closeModal"
                    class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body - Filters -->
            <div class="p-6 space-y-6">

                <!-- market type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Market type</label>
                    <div class="grid grid-cols-3 gap-2">
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="1" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Indian</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="2" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Forex</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="3" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Crypto</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="0" class="modern-radio" checked>
                            <span class="ml-2 text-gray-700 dark:text-gray-300">All</span>
                        </label>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start
                            Date</label>
                        <div class="relative">
                            <input type="date" id="startDate"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white modern-input">
                            <i class="fas fa-calendar absolute right-3 top-3 text-gray-400 pointer-events-none"></i>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
                        <div class="relative">
                            <input type="date" id="endDate"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white modern-input">
                            <i class="fas fa-calendar absolute right-3 top-3 text-gray-400 pointer-events-none"></i>
                        </div>
                    </div>
                </div>

                <!-- Strategy Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Trading
                        Strategy</label>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                        <?php foreach ($strategies as $s => $strategy) { ?>
                            <label
                                class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                                <input type="checkbox" value="<?= $strategy['id'] ?>" class="modern-checkbox">
                                <span class="ml-2 text-gray-700 dark:text-gray-300"><?= $strategy['strategy'] ?></span>
                            </label>
                        <?php } ?>
                    </div>
                </div>

                <!-- Direction -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Trade
                        Direction</label>
                    <div class="grid grid-cols-3 gap-2">
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="1" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Long</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="2" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Short</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="0" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Both</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end space-x-3 border-t dark:border-gray-600">
                <button id="resetFilters"
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition modern-btn modern-btn-secondary">
                    Reset Filters
                </button>
                <button id="applyFilters" onclick="applyFilters()"
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition modern-btn modern-btn-primary">
                    Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Add Trade Modal -->
    <div id="addTradeModal"
        class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 hidden backdrop-blur-sm">
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            <!-- Fixed Header -->
            <div
                class="px-6 py-3 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center sticky top-0 bg-white dark:bg-gray-800 z-10">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">Add New Trade</h2>
                </div>
                <button id="closeTradeModal"
                    class="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Scrollable Body -->
            <div class="overflow-y-auto flex-grow">
                <div class="px-6 py-3">
                    <!-- Tab Navigation -->
                    <div class="flex border-b border-gray-200 dark:border-gray-700 mb-6">
                        <button class="nav-tab active px-4 py-2 font-medium text-gray-800 dark:text-gray-200 mr-1"
                            id="trade-tab" data-tab="basic-tab">
                            <i class="fas fa-info-circle mr-2"></i> General
                        </button>
                        <button
                            class="nav-tab px-4 py-2 font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mr-1"
                            data-tab="psychology-tab">
                            <i class="fas fa-brain mr-2"></i> Psychology
                        </button>
                    </div>

                    <!-- Trade Form -->
                    <form id="tradeForm" class="mb-6" enctype="multipart/form-data" method="post" action="#">
                        <!-- Basic Information Tab -->
                        <input type="hidden" id="editId" name="editId" value="">
                        <div id="basic-tab" class="tab-content active">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                <!-- Type -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Market
                                        type<sup class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Market type" id="market_type" name="market_type">
                                        <option value="1">Indian</option>
                                        <!-- <option value="2">Forex</option> -->
                                        <option value="3">Crypto</option>
                                    </select>
                                </div>


                                <!-- Symbol -->
                                <div class="sm:col-span-2 lg:col-span-1">
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Symbol<sup
                                            class="text-red-400"> *</sup></label>
                                    <div class="relative">
                                        <input type="text"
                                            class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                            placeholder="RELIANCE, NIFTY 50, etc." id="symbol" name="symbol"
                                            data-inputname="Symbol">
                                        <div
                                            class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <!-- <i class="fas fa-search text-gray-400"></i> -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Date & Time -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Date<sup
                                            class="text-red-400"> *</sup></label>
                                    <input type="date" class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Date" id="datetime" name="datetime">
                                </div>

                                <!-- Entry Price -->
                                <div>
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Entry
                                        Price
                                        <sup class="text-red-400"> *</sup></label>
                                    <input type="number" step="0.01"
                                        class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Entry Price" id="entry_price" placeholder="Entry Price"
                                        name="entry_price">
                                </div>

                                <!-- Position Size -->
                                <div>

                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Quantity<sup
                                                    class="text-red-400"> *</sup></label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="Quantity" id="entry_quantity" name="entry_quantity"
                                                placeholder="Quantity">
                                        </div>
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Total
                                                amount</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="Total
                                        amount" id="entry_amount" name="entry_amount" placeholder=" Amount" readonly>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exit Price -->
                                <div>
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Exit
                                        Price
                                        <sup class="text-red-400"> *</sup></label>
                                    <input type="number" step="0.01" id="exit_price" name="exit_price"
                                        class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Exit Price" placeholder="Exit Price">
                                </div>

                                <!-- Profit / Loss -->
                                <div>

                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">P&L
                                                Amount</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="P&L Amount" id="pnlAmount" name="pnlAmount"
                                                placeholder=" Amount" readonly>
                                        </div>
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">P&L
                                                (%)</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="P&L (%)" id="pnlPercent" name="pnlPercent"
                                                placeholder="% Change" readonly>
                                        </div>
                                    </div>
                                </div>

                                <!-- Direction -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Direction<sup
                                            class="text-red-400"> *</sup></label>
                                    <div class="flex space-x-2">
                                        <button type="button" id="btn-long"
                                            class="flex-1 py-2 px-3 rounded-lg border border-green-500 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-300 font-medium flex items-center justify-center">
                                            <i class="fas fa-arrow-up mr-2"></i> Long
                                        </button>
                                        <button type="button" id="btn-short"
                                            class="flex-1 py-2 px-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium flex items-center justify-center">
                                            <i class="fas fa-arrow-down mr-2"></i> Short
                                        </button>
                                    </div>
                                    <input type="hidden" id="trade_type" name="trade_type" class="tReq"
                                        data-inputname="Trade Direction" value="1">
                                </div>

                                <!-- Stop Loss & Target -->
                                <div>
                                    <div class="col-span-12 md:col-span-4">
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                            <div>
                                                <label
                                                    class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Stop
                                                    Loss</label>
                                                <input type="number" step="0.01" id="stop_loss" name="stop_loss"
                                                    class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                    placeholder="Stop Loss">
                                            </div>
                                            <div>
                                                <label
                                                    class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Target
                                                </label>
                                                <input type="number" step="0.01" id="target" name="target"
                                                    class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                    placeholder="Target">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Strategy -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Strategy<sup
                                            class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Strategy" id="strategy" name="strategy">
                                        <option value="">Select Strategy</option>
                                        <?php foreach ($strategies as $key => $str) { ?>
                                            <option value="<?= $str['id'] ?>"><?= $str['strategy'] ?></option>
                                        <?php } ?>
                                    </select>
                                </div>

                                <!-- Outcome Summary -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Outcome
                                        Summary<sup class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Outcome summary" id="outcome" name="outcome">
                                        <option value="">Select Outcome Summary</option>
                                        <?php foreach ($summaries as $key => $smr) { ?>
                                            <option value="<?= $smr['id'] ?>"><?= $smr['summary'] ?></option>
                                        <?php } ?>
                                    </select>
                                </div>

                                <!-- Trade Rationale & Rules Followed -->
                                <div class="sm:col-span-2 lg:col-span-3">
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Trade
                                        Analysis</label>
                                    <textarea rows="4" class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                        data-inputname="Trade Analysis" id="rationale" name="rationale"
                                        placeholder="Why did you take this trade? What was your analysis?"></textarea>

                                    <label class="block text-sm font-medium mb-1 mt-4 text-gray-700 dark:text-gray-300">
                                        Rules Followed
                                    </label>
                                    <div
                                        class="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-gray-50 dark:bg-gray-700/30">
                                        <div id="selectedRules" class="flex flex-wrap mb-3"></div>
                                        <div id="selectedRuleInputs"></div>

                                        <div class="relative">
                                            <input type="text" id="ruleSearch"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                placeholder="Search or add rules...">

                                            <div id="ruleDropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                                <?php foreach ($rules as $key => $rl) { ?>
                                                    <div class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                                                        data-rule="<?= $rl['id'] ?>">
                                                        <?= htmlspecialchars($rl['rule']) ?>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>

                                    <script>
                                        // Build JS rules object from PHP
                                        const rules = {
                                            <?= implode(",\n", array_map(function ($rl) {
                                                return "'" . $rl['id'] . "': " . json_encode($rl['rule']);
                                            }, $rules)) ?>
                                        };
                                    </script>

                                </div>

                                <!-- File Upload -->
                                <div class="sm:col-span-2 lg:col-span-3">
                                    <label class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Trade
                                        Screenshots</label>
                                    <div
                                        class="file-upload rounded-lg p-8 text-center cursor-pointer transition-all duration-200">
                                        <div
                                            class="w-16 h-16 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <i
                                                class="fas fa-cloud-upload-alt text-2xl text-blue-500 dark:text-blue-400"></i>
                                        </div>
                                        <p class="text-gray-500 dark:text-gray-400 mb-1 font-medium">Drag & drop your
                                            trade
                                            screenshots here</p>
                                        <p class="text-xs text-gray-400">Supports JPG, PNG (Max 5MB each)</p>
                                        <input type="file" class="hidden" name="screenshots[]" multiple
                                            accept="image/*">
                                    </div>
                                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4"
                                        id="previewContainer"></div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-span-6 md:col-span-3 bg-red-200 p-4">Col 3</div> -->

                        <!-- Psychological Review Tab -->
                        <div id="psychology-tab" class="tab-content">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Column 1 -->
                                <div class="space-y-6">
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Entry
                                            Confidence Level (1-10)</label>
                                        <div class="flex items-center space-x-4">
                                            <input type="range" min="1" max="10" value="5" id="confidence"
                                                name="confidence" class="w-full slider-thumb" />
                                            <span
                                                class="text-lg font-medium w-8 text-center text-gray-700 dark:text-gray-300"
                                                id="confidenceValue">5</span>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <span>Low</span>
                                            <span>Medium</span>
                                            <span>High</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Satisfaction
                                            Rating (1-10)</label>
                                        <div class="flex items-center space-x-4">
                                            <input type="range" min="1" max="10" value="5" class="w-full slider-thumb"
                                                id="satisfaction" name="satisfaction">
                                            <span
                                                class="text-lg font-medium w-8 text-center text-gray-700 dark:text-gray-300"
                                                id="executionValue">5</span>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <span>Not Satisfied</span>
                                            <span>Average</span>
                                            <span>Satisfied</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Emotional
                                            State During Trade<sup class="text-red-400"> *</sup></label>
                                        <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                            data-inputname="Emotional State" id="emotion" name="emotion">
                                            <option value="">Select Emotional State</option>
                                            <?php foreach ($emotions as $key => $em) { ?>
                                                <option value="<?= $em['id'] ?>"><?= $em['emotion'] ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Column 2 -->
                                <div class="space-y-6">
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Mistakes
                                            Made</label>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                            <?php foreach ($mistakes as $key => $mst) { ?>
                                                <div class="checkbox-item flex items-center">
                                                    <label for="mistake<?= $mst['id'] ?>"
                                                        class="checkbox-item flex items-center cursor-pointer">
                                                        <input type="checkbox" id="mistake<?= $mst['id'] ?>"
                                                            name="mistakes[]" value="<?= $mst['id'] ?>" class="hidden peer">

                                                        <span
                                                            class="checkmark w-4 h-4 border border-gray-400 mr-2 rounded-sm peer-checked:bg-red-500"></span>

                                                        <span class="text-sm"><?= $mst['mistake'] ?></span>
                                                    </label>
                                                </div>
                                            <?php } ?>
                                        </div>

                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Lessons
                                            Learned</label>
                                        <textarea rows="3" class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                            data-inputname="Lessons Learned" id="lesson" name="lesson"
                                            placeholder="What did you learn from this trade?"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Fixed Footer -->
            <div
                class="px-6 py-3 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-3 sticky bottom-0 bg-white dark:bg-gray-800 z-10">
                <button type="button" id="resetFormBtn"
                    class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 font-medium">
                    <i class="fas fa-redo mr-2"></i> Reset
                </button>
                <button type="button" id="saveTradeBtn" onclick="saveTrade('<?= base_url('saveTrade') ?>')"
                    class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white px-6 py-2 rounded-lg flex items-center font-medium shadow-sm hover:shadow-md transition-all duration-200 glow-effect">
                    <i class="fas fa-save mr-2"></i> Save Trade
                </button>
            </div>
        </div>
    </div>

    <div id="successModal" class="modal">
        <div class="modal-content p-8 success-modal">
            <div class="mb-6">
                <div
                    class="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check-circle text-4xl text-green-500 dark:text-green-400"></i>
                </div>
                <h3 class="text-2xl font-bold text-center mb-2 text-gray-800 dark:text-gray-200">Trade Saved
                    Successfully!</h3>
                <p id="response_message" class="text-gray-600 dark:text-gray-300 text-center">...</p>
            </div>

            <div class="trade-comparison">
                <h4 class="font-medium text-lg mb-2 text-gray-800 dark:text-gray-200">Comparison with
                    Previous Day's P&L</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Today's P&L</div>
                        <div class="font-medium" id="todays_pnl">0.00</div>
                        <!-- <div class="text-xs text-gray-500 dark:text-gray-400">Breakout Strategy</div> -->
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Yesterday's P&L</div>
                        <div class="font-medium" id="yesterdays_pnl">0.00</div>
                        <!-- <div class="text-xs text-gray-500 dark:text-gray-400">Reversal Strategy</div> -->
                    </div>
                </div>
                <div class="mt-3 text-sm text-gray-600 dark:text-gray-300">
                    <i id="message_icon" class=""></i>
                    <span id="comparison_message">...</span>
                </div>
            </div>

            <div class="mt-6 flex justify-center">
                <button id="closeSuccessModal"
                    class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white px-6 py-2 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200">
                    Keep Journaling
                </button>
            </div>
        </div>
    </div>

    <!-- Import Trades Modal -->
    <div id="importTradesModal"
        class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 hidden backdrop-blur-sm">
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            <!-- Fixed Header -->
            <div
                class="px-6 py-3 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center sticky top-0 bg-white dark:bg-gray-800 z-10">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">Import Trades</h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Upload CSV or Excel files from your broker</p>
                </div>
                <button id="closeImportModal"
                    class="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Scrollable Body -->
            <div class="overflow-y-auto flex-grow">
                <div class="px-6 py-3">
                    <!-- Step 1: File Upload -->
                    <div id="step1" class="import-step">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Step 1: Upload File</h3>

                        <div class="mb-4">
                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-blue-800 dark:text-blue-200">Supported Formats</h4>
                                        <p class="text-sm text-blue-600 dark:text-blue-300 mt-1">
                                            CSV (.csv) files up to 10MB<br>
                                            Supports exports from Zerodha, Angel One, ICICI Direct, and other brokers<br>
                                            <small class="text-blue-500">Excel support coming soon - please export as CSV for now</small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form id="importForm" enctype="multipart/form-data">
                            <div
                                class="file-upload-area border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer transition-all duration-200 hover:border-blue-400 dark:hover:border-blue-500">
                                <div
                                    class="w-16 h-16 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-cloud-upload-alt text-2xl text-blue-500 dark:text-blue-400"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-400 mb-1 font-medium">Drag & drop your trade file here</p>
                                <p class="text-xs text-gray-400">or click to browse</p>
                                <input type="file" id="importFile" name="import_file" class="hidden" accept=".csv">
                            </div>

                            <div id="fileInfo" class="mt-4 hidden">
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-alt text-gray-500 mr-3"></i>
                                            <div>
                                                <p id="fileName" class="font-medium text-gray-800 dark:text-gray-200"></p>
                                                <p id="fileSize" class="text-sm text-gray-500 dark:text-gray-400"></p>
                                            </div>
                                        </div>
                                        <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Step 2: Preview & Mapping -->
                    <div id="step2" class="import-step hidden">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Step 2: Preview & Column Mapping</h3>

                        <div class="mb-4">
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <div>
                                        <p class="font-medium text-green-800 dark:text-green-200">File parsed successfully!</p>
                                        <p id="previewInfo" class="text-sm text-green-600 dark:text-green-300"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Column Mapping -->
                        <div class="mb-6">
                            <h4 class="font-medium mb-3 text-gray-800 dark:text-gray-200">Column Mapping</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="columnMapping">
                                <!-- Mapping controls will be inserted here -->
                            </div>
                        </div>

                        <!-- Data Preview -->
                        <div>
                            <h4 class="font-medium mb-3 text-gray-800 dark:text-gray-200">Data Preview (First 5 rows)</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <thead id="previewHeaders" class="bg-gray-50 dark:bg-gray-700">
                                        <!-- Headers will be inserted here -->
                                    </thead>
                                    <tbody id="previewData" class="bg-white dark:bg-gray-800">
                                        <!-- Preview data will be inserted here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Import Progress -->
                    <div id="step3" class="import-step hidden">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Step 3: Import Progress</h3>

                        <div class="text-center py-8">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full mb-4">
                                <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
                            </div>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Processing your trades...</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Please wait while we import your data</p>
                        </div>
                    </div>

                    <!-- Step 4: Import Results -->
                    <div id="step4" class="import-step hidden">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Import Complete</h3>

                        <div id="importResults">
                            <!-- Results will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fixed Footer -->
            <div
                class="px-6 py-3 border-t border-gray-200 dark:border-gray-600 flex justify-between sticky bottom-0 bg-white dark:bg-gray-800 z-10">
                <button type="button" id="prevStepBtn"
                    class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 font-medium hidden">
                    <i class="fas fa-arrow-left mr-2"></i> Previous
                </button>

                <div class="flex space-x-3">
                    <button type="button" id="cancelImportBtn"
                        class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 font-medium">
                        Cancel
                    </button>
                    <button type="button" id="nextStepBtn"
                        class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white px-6 py-2 rounded-lg flex items-center font-medium shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                        <span id="nextStepText">Upload File</span>
                        <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="viewTradeDetailsModal"
        class="fixed inset-0 z-9999 flex items-center justify-center p-4 modal-bg backdrop-blur-sm hidden modal">
        <!-- Modal Container -->
        <div
            class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden animate-slide-up transition-all duration-300">
            <!-- Modal Header -->
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100">Trade Summary</h3>
                <div class="flex items-center space-x-4">
                    <button id="closeModalDet"
                        class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <div class="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-10">
                <div class="flex overflow-x-auto">
                    <button data-tab="general"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-info-circle"></i>
                        <span>General</span>
                    </button>
                    <button data-tab="analytics"
                        class="hidden tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics</span>
                    </button>
                    <button data-tab="journal"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-book"></i>
                        <span>Journal</span>
                    </button>
                    <button data-tab="media"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-images"></i>
                        <span>Media</span>
                    </button>
                </div>
            </div>

            <!-- Tab Contents -->
            <div class="overflow-y-auto p-6" style="max-height: calc(90vh - 120px)">
                <!-- General Tab -->
                <div id="general" class="tab-content tab-content-det active">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Trade Overview -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-tag"></i>
                                <span>Trade Overview</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Symbol</span>
                                    <span id="viewSymbol"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Direction</span>
                                    <span id="viewDirection" class="font-medium">...</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Entry Price</span>
                                    <span id="viewEntryPrice" class="font-medium text-gray-800 dark:text-gray-100">
                                        0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Exit Price</span>
                                    <span id="viewExitPrice" class="font-medium text-gray-800 dark:text-gray-100">
                                        0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Trade Session</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-100">2 days 4 hours</span>
                                </div>
                            </div>
                        </div>

                        <!-- Risk Management -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-shield-alt"></i>
                                <span>Risk Management</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Position Size</span>
                                    <span id="viewPositionSize" class="font-medium text-gray-800 dark:text-gray-100">0
                                        Qty</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Stop Loss</span>
                                    <span id="viewStopLoss" class="font-medium text-red-500"> 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Target</span>
                                    <span id="viewTarget" class="font-medium text-green-500"> 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Expected Risk/Reward</span>
                                    <span id="viewRrRatio"
                                        class="font-medium text-gray-800 dark:text-gray-100">1:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Risk/Reward %</span>
                                    <span id="viewRiskPercent"
                                        class="font-medium text-gray-800 dark:text-gray-100">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Performance -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-trophy"></i>
                                <span>Performance</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">P&L</span>
                                    <span id="viewPnl" class="font-medium text-green-500"> 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">P&L %</span>
                                    <span id="viewPnlPercent" class="font-medium text-green-500">0.00%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Final Risk/Reward</span>
                                    <span id="viewActualRR"
                                        class="font-medium text-gray-800 dark:text-gray-100">0:0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Trade Outcome</span>
                                    <span id="viewOutcome"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Evaluation -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-star"></i>
                                <span>Trade Evaluation</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600 dark:text-gray-300">Entry Confidence</span>
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                            <div id="viewConfidence" class="bg-yellow-500 h-2.5 rounded-full"
                                                style="width: 75%"></div>
                                        </div>
                                        <span id="viewConfidenceTExt"
                                            class="ml-2 text-sm font-medium text-gray-800 dark:text-gray-100">75%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600 dark:text-gray-300">Satisfaction</span>
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                            <div id="viewSatisfaction" class="bg-green-500 h-2.5 rounded-full"
                                                style=" width: 85%"></div>
                                        </div>
                                        <span id="viewSatisfactionText"
                                            class="ml-2 text-sm font-medium text-gray-800 dark:text-gray-100">85%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Emotional State</span>
                                    <span id="viewEmotion"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                                <div class="flex justify-between items-center hidden">
                                    <span class="text-gray-600 dark:text-gray-300">Satisfaction</span>
                                    <div class="flex">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Strategy</span>
                                    <span id="viewStrategy"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Market Conditions -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-chart-bar"></i>
                                <span>Market Conditions</span>
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Volatility Index</p>
                                    <p class="text-lg font-bold text-purple-500">68.2</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Market Sentiment</p>
                                    <p class="text-lg font-bold text-green-500">Bullish</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Liquidity</p>
                                    <p class="text-lg font-bold text-blue-500">High</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Trend Strength</p>
                                    <p class="text-lg font-bold text-yellow-500">Medium</p>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Metrics -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-ruler-combined"></i>
                                <span>Trade Metrics</span>
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Win Probability</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 72%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">72%
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Trade Efficiency</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">85%
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Risk Adjusted Return</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-purple-500 h-2.5 rounded-full" style="width: 64%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">1.42
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Timeline -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-timeline"></i>
                                <span>Trade Timeline</span>
                            </h4>
                            <div class="relative">
                                <div class="absolute left-4 h-full w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                                <div class="space-y-6">
                                    <!-- Entry -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                            <i class="fas fa-arrow-up text-blue-600 dark:text-blue-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Entry</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    15, 14:00 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Entered long at
                                                $28,450 with 0.5 BTC position</p>
                                        </div>
                                    </div>

                                    <!-- Stop Loss Adjustment -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                                            <i class="fas fa-adjust text-yellow-600 dark:text-yellow-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Stop Loss
                                                    Adjustment</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    15, 18:30 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Moved stop loss
                                                from $27,500 to $27,800 after initial pullback</p>
                                        </div>
                                    </div>

                                    <!-- Take Profit Hit -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Partial Exit
                                                </h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    17, 10:15 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Exited 50% position
                                                at $29,120 (80% of target)</p>
                                        </div>
                                    </div>

                                    <!-- Full Exit -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                                            <i class="fas fa-flag-checkered text-purple-600 dark:text-purple-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Full Exit</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    17, 18:00 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Closed remaining
                                                position at $29,120 for total profit of $335.21</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Strategy Performance -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-chart-pie"></i>
                                <span>Strategy Performance</span>
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Total Trades</p>
                                            <p class="text-xl font-bold text-gray-800 dark:text-gray-100">24</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Win Rate</p>
                                            <p class="text-xl font-bold text-green-500">68%</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-300">
                                            <i class="fas fa-trophy"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Avg Profit</p>
                                            <p class="text-xl font-bold text-purple-500">2.1%</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-300">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Expectancy</p>
                                            <p class="text-xl font-bold text-yellow-500">0.45</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-300">
                                            <i class="fas fa-balance-scale"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Journal Tab -->
                <div id="journal" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Trade Rationale -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-lightbulb text-yellow-500"></i>
                                    <span>Trade Rationale</span>
                                </h4>
                                <span id="viewStrategyBadge"
                                    class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full">...</span>
                            </div>
                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4">
                                <textarea readonly id="viewRationale"
                                    class="w-full h-32 p-3 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                                    placeholder="Describe your trade rationale here...">...</textarea>
                            </div>
                        </div>

                        <!-- Mistakes Made -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                                    <span>Mistakes Made</span>
                                </h4>
                                <!-- <button
                                    class="text-xs px-3 py-1 bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                                    <i class="fas fa-plus mr-1"></i> Add Mistake
                                </button> -->
                            </div>

                            <div id="viewMistakesContainer" class="space-y-3">
                                <!-- dynamically append mistakes -->
                            </div>
                        </div>

                        <!-- Emotional State -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-heart text-pink-500"></i>
                                    <span>Emotional State</span>
                                </h4>
                                <div class="flex items-center space-x-2">
                                    <span
                                        class="text-xs px-2 py-1 bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 rounded-full">Moderate
                                        Stress</span>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4 mb-4">
                                <p class="text-gray-700 dark:text-gray-200 mb-3">
                                    During this trade, I experienced a mix of emotions. Initially, I felt confident
                                    about the setup but became nervous during the first pullback. As the trade moved in
                                    my favor, excitement grew, but I also felt some regret for not taking partial
                                    profits earlier when the price stalled near my initial target.
                                </p>
                                <p class="text-gray-700 dark:text-gray-200">
                                    The most challenging moment was when price retraced nearly to my entry point - I
                                    felt tempted to exit early to avoid a loss, but stuck to my plan. Overall, I'm
                                    satisfied with how I managed my emotions, though I recognize room for improvement in
                                    maintaining composure during drawdowns.
                                </p>
                            </div>

                            <div class="grid grid-cols-2 sm:grid-cols-5 gap-3">
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-smile text-blue-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Confident</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-flushed text-yellow-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Nervous</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-grin-stars text-green-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Excited</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-sad-tear text-purple-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Regretful</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-angry text-red-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Frustrated</span>
                                </button>
                            </div>
                        </div>

                        <!-- Lessons Learned -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                                <i class="fas fa-graduation-cap text-indigo-500"></i>
                                <span>Lessons Learned</span>
                            </h4>
                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4">
                                <textarea readonly id="viewLesson"
                                    class="w-full h-32 p-3 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                                    placeholder="What lessons did you learn from this trade?">...</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Media Tab -->
                <div id="media" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Screenshots -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-camera"></i>
                                <span>Screenshots</span>
                            </h4>
                            <div id="viewImgContainer" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                                <!-- Images will be injected here -->
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-end">
                <button
                    class="hidden px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors mr-2">
                    Export
                </button>
                <button id="closeTradeViewButton"
                    class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-lg hover:bg-blue-600 dark:hover:bg-blue-500 transition-colors">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>




<!-- POPUPS -->
<div id="recordProfitPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('recordProfitPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-4 text-blue-500 dark:text-blue-400 text-xl">
                <i class="fas fa-trophy"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">New Record Profit</h2>
                <p class="text-sm text-blue-500 dark:text-blue-400">Personal best achievement</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-blue-500 dark:text-blue-400">$1,250</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Profit</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-green-500 dark:text-green-400">+24%</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">From previous</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-purple-500 dark:text-purple-400">8.2</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Confidence</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Breakout Strategy</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400">High
                    Conviction</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Your execution was textbook perfect. You remained
                disciplined throughout and followed your trading plan exactly.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 dark:text-yellow-400 mr-2"></i> Key Insight
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">This success demonstrates your strategy's effectiveness.
                Consider gradually scaling in similar setups while maintaining strict risk management.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('recordProfitPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-book-open mr-2"></i> Review
            </button>
            <button onclick="celebrate(); hidePopup('recordProfitPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition flex items-center justify-center">
                Celebrate <i class="fas fa-glass-cheers ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="recordLossPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('recordLossPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-4 text-red-500 dark:text-red-400 text-xl">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Significant Loss</h2>
                <p class="text-sm text-red-500 dark:text-red-400">Learning opportunity</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-red-500 dark:text-red-400">$850</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Loss</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-orange-500 dark:text-orange-400">9.0</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Confidence</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-pink-500 dark:text-pink-400">3.2</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Control</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Reversal Strategy</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400">Against
                    Trend</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Your journal notes indicate frustration and unusually
                high confidence despite unfavorable market conditions.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-graduation-cap text-blue-500 dark:text-blue-400 mr-2"></i> Key Lesson
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Major losses often precede breakthroughs. Review your
                risk parameters - was position size appropriate? What early warning signs did you overlook?</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('recordLossPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-book mr-2"></i> Journal
            </button>
            <button onclick="hidePopup('recordLossPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-red-500 hover:bg-red-600 text-white transition flex items-center justify-center">
                Analyze <i class="fas fa-search ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="improvementPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('improvementPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-4 text-green-500 dark:text-green-400 text-xl">
                <i class="fas fa-chart-line"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Noticeable Improvement</h2>
                <p class="text-sm text-green-500 dark:text-green-400">Progress detected</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-green-500 dark:text-green-400">$320</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Today</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-gray-700 dark:text-gray-300">$150</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Yesterday</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-teal-500 dark:text-teal-400">+113%</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Improvement</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Breakout Strategy</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400">Improved
                    Timing</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Your entry timing improved by 12% and emotional control
                was steadier (6.8 vs 5.2 yesterday).</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-bullseye text-teal-500 dark:text-teal-400 mr-2"></i> Key Takeaway
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Small, consistent improvements compound over time. What
                specific adjustment led to today's better result? Document it for future reference.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('improvementPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-chart-pie mr-2"></i> Compare
            </button>
            <button onclick="hidePopup('improvementPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-green-500 hover:bg-green-600 text-white transition flex items-center justify-center">
                Continue <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="declinePopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('declinePopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center mr-4 text-yellow-500 dark:text-yellow-400 text-xl">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Performance Dip</h2>
                <p class="text-sm text-yellow-500 dark:text-yellow-400">Temporary setback</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-yellow-500 dark:text-yellow-400">$90</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Today</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-gray-700 dark:text-gray-300">$210</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Yesterday</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-amber-500 dark:text-amber-400">-57%</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Decline</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Pullback Strategy</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400">Rushed
                    Entry</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Your journal shows you entered prematurely (confidence
                6/10) when normally you wait for confirmation signals.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-undo text-amber-500 dark:text-amber-400 mr-2"></i> Recovery Plan
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">One step back doesn't undo progress. Revisit your
                checklist - was today's trade truly aligned with your strategy parameters?</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('declinePopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-clipboard-list mr-2"></i> Rules
            </button>
            <button onclick="hidePopup('declinePopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-yellow-500 hover:bg-yellow-600 text-white transition flex items-center justify-center">
                Reset <i class="fas fa-sync-alt ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="winStreakPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('winStreakPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mr-4 text-purple-500 dark:text-purple-400 text-xl">
                <i class="fas fa-fire"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">5 Wins Straight</h2>
                <p class="text-sm text-purple-500 dark:text-purple-400">Hot streak</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-purple-500 dark:text-purple-400">5</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Wins</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-pink-500 dark:text-pink-400">$1,920</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Total</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-fuchsia-500 dark:text-fuchsia-400">1:2.5</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Risk/Reward</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Trend Following</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400">Consistent</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Average confidence was 7.4/10 and you've maintained
                excellent risk/reward discipline throughout.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-balance-scale text-pink-500 dark:text-pink-400 mr-2"></i> Balanced Approach
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">While celebrating, stay grounded. Markets evolve - are
                you adapting or getting lucky? Consider protecting profits with trailing stops.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('winStreakPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-chart-bar mr-2"></i> Analyze
            </button>
            <button onclick="celebrate(); hidePopup('winStreakPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-purple-500 hover:bg-purple-600 text-white transition flex items-center justify-center">
                Focus <i class="fas fa-bullseye ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="lossStreakPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('lossStreakPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center mr-4 text-orange-500 dark:text-orange-400 text-xl">
                <i class="fas fa-cloud-rain"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">3 Losses Straight</h2>
                <p class="text-sm text-orange-500 dark:text-orange-400">Drawdown detected</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-orange-500 dark:text-orange-400">3</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Losses</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-red-500 dark:text-red-400">$480</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Total</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-rose-500 dark:text-rose-400">2.8</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Control</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Multiple Strategies</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400">Inconsistent</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Your journal shows increasing frustration and slightly
                larger position sizes with each consecutive loss.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-hand-paper text-red-500 dark:text-red-400 mr-2"></i> Action Plan
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Streaks happen to all traders. Consider: 1) Reduce size
                by 50% 2) Review last winning trades 3) Wait 24h before next trade.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('lossStreakPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-history mr-2"></i> Review
            </button>
            <button onclick="hidePopup('lossStreakPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-orange-500 hover:bg-orange-600 text-white transition flex items-center justify-center">
                Reset <i class="fas fa-redo ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="repeatedMistakePopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('repeatedMistakePopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center mr-4 text-pink-500 dark:text-pink-400 text-xl">
                <i class="fas fa-redo"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Repeated Pattern</h2>
                <p class="text-sm text-pink-500 dark:text-pink-400">Behavioral insight</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-pink-500 dark:text-pink-400">3</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Occurrences</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-rose-500 dark:text-rose-400">$620</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Impact</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-fuchsia-500 dark:text-fuchsia-400">82%</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Same context</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Early Entries</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-400">Ranging
                    Markets</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">You've entered before confirmation in ranging markets
                multiple times this month, often citing "impatience" in your notes.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-brain text-rose-500 dark:text-rose-400 mr-2"></i> Cognitive Fix
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Try this: 1) 5-minute delay before entering 2) Require 3
                confirming indicators 3) Journal why you want to enter early before acting.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('repeatedMistakePopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-clipboard-check mr-2"></i> Create Rule
            </button>
            <button onclick="hidePopup('repeatedMistakePopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-pink-500 hover:bg-pink-600 text-white transition flex items-center justify-center">
                Break Cycle <i class="fas fa-unlock ml-2"></i>
            </button>
        </div>
    </div>
</div>

<div id="perfectExecutionPopup" class="popup-container">
    <div
        class="popup bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md relative overflow-hidden border border-gray-200 dark:border-gray-700">
        <button onclick="hidePopup('perfectExecutionPopup')"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition">
            <i class="fas fa-times"></i>
        </button>

        <div class="flex items-start mb-6">
            <div
                class="w-12 h-12 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mr-4 text-indigo-500 dark:text-indigo-400 text-xl">
                <i class="fas fa-check-double"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Perfect Execution</h2>
                <p class="text-sm text-indigo-500 dark:text-indigo-400">Textbook trade</p>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-3 mb-6">
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-indigo-500 dark:text-indigo-400">$45</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Profit</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-blue-500 dark:text-blue-400">8.0</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Discipline</div>
            </div>
            <div class="stat-card bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-center">
                <div class="text-2xl font-bold text-sky-500 dark:text-sky-400">100%</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Rules followed</div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Pullback Strategy</span>
                <span
                    class="text-xs px-2 py-1 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400">Perfect
                    Setup</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Waited for confirmation, sized correctly, managed
                emotions (8/10), and exited exactly per your trading plan.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="fas fa-medal text-blue-500 dark:text-blue-400 mr-2"></i> Professional Mindset
            </h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Discipline compounds more than any single trade. This is
                how professionals operate - consistently executing their edge without emotion.</p>
        </div>

        <div class="flex justify-between gap-3">
            <button onclick="hidePopup('perfectExecutionPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 transition flex items-center justify-center">
                <i class="fas fa-star mr-2"></i> Bookmark
            </button>
            <button onclick="hidePopup('perfectExecutionPopup')"
                class="flex-1 px-4 py-2 rounded-lg bg-indigo-500 hover:bg-indigo-600 text-white transition flex items-center justify-center">
                Well Done <i class="fas fa-thumbs-up ml-2"></i>
            </button>
        </div>
    </div>
</div>

<!-- Kite Login Modal -->
<div id="kiteLoginModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg w-[450px] h-[650px] relative shadow-lg">
        <button onclick="closeKiteLogin()" class="absolute top-2 right-2 text-gray-600 hover:text-black">✕</button>
        <iframe id="kiteLoginFrame" class="w-full h-full rounded-b-lg border-none" src=""
            allow="clipboard-write"></iframe>
    </div>
</div>


<div id="kite-token-popup" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center hidden">
    <div class="popup-content w-full max-w-md">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100" id="broker-api-title">Add Kite
                        Token</h3>
                    <button onclick="closeModal('#kite-token-popup')" id="close-broker-api"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <form id="kite-token-form">
                    <div class="mb-4">
                        <label for="kite-tokenApiKey"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Token</label>
                        <input type="text" id="kite-tokenApiKey" name="kite-tokenApiKey"
                            class="kite-tokenReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Paste the copied token here">
                        <span class="error-message text-red-500 hidden">Kite token is required</span>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button"
                            onclick="generateKiteToken('kite-token','<?= base_url('generateKiteToken') ?>','.kite-tokenReq','#kite-token-form')"
                            class="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-600 transition flex items-center">
                            <span>Update token</span>
                            <i id="connect-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>