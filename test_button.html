<!DOCTYPE html>
<html>
<head>
    <title>Test Import Button</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .hidden { display: none; }
        .import-step { padding: 20px; }
        .file-upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; }
    </style>
</head>
<body>
    <button id="importTradesBtn">Import Trades</button>
    
    <div id="importTradesModal" class="hidden">
        <h2>Import Trades Modal</h2>
        <div id="step1" class="import-step">
            <h3>Step 1: Upload File</h3>
            <form id="importForm">
                <div class="file-upload-area">
                    <p>Drag & drop your file here</p>
                    <input type="file" id="importFile" name="import_file" class="hidden">
                </div>
                <div id="fileInfo" class="hidden">
                    <p>File: <span id="fileName"></span></p>
                    <p>Size: <span id="fileSize"></span></p>
                    <button type="button" id="removeFile">Remove</button>
                </div>
            </form>
        </div>
        <button id="closeImportModal">Close</button>
        <button id="cancelImportBtn">Cancel</button>
        <button id="nextStepBtn" disabled>
            <span id="nextStepText">Upload File</span>
        </button>
        <button id="prevStepBtn" class="hidden">Previous</button>
    </div>

    <script>
        const base_url = './';
        let importData = {};
        let currentStep = 1;

        $(document).ready(function () {
            // Import Trades functionality
            $('#importTradesBtn').click(function () {
                console.log('Import button clicked');
                $('#importTradesModal').removeClass('hidden');
                resetImportModal();
            });

            $('#closeImportModal, #cancelImportBtn').click(function () {
                $('#importTradesModal').addClass('hidden');
                resetImportModal();
            });

            // File upload handling
            $('.file-upload-area').click(function () {
                $('#importFile').click();
            });

            $('#importFile').change(function () {
                const file = this.files[0];
                if (file) {
                    handleFileSelect(file);
                }
            });

            $('#removeFile').click(function () {
                $('#importFile').val('');
                $('#fileInfo').addClass('hidden');
                $('#nextStepBtn').prop('disabled', true);
            });

            function resetImportModal() {
                currentStep = 1;
                importData = {};
                
                // Reset form
                $('#importForm')[0].reset();
                $('#fileInfo').addClass('hidden');
                $('#importFile').val('');
                $('#nextStepBtn').prop('disabled', true).find('#nextStepText').text('Upload File');
            }

            function handleFileSelect(file) {
                console.log('File selected:', file.name);
                
                // Show file info
                $('#fileName').text(file.name);
                $('#fileSize').text(formatFileSize(file.size));
                $('#fileInfo').removeClass('hidden');
                
                // Enable next button
                $('#nextStepBtn').prop('disabled', false);
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            function createToast(type, title, message) {
                console.log(`${type}: ${title} - ${message}`);
                alert(`${type}: ${title} - ${message}`);
            }
        });
    </script>
</body>
</html>
